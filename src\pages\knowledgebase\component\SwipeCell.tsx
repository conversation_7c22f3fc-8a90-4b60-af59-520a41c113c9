import React, { useRef, useImperativeHandle, forwardRef } from 'react';
// import Taro from '@tarojs/taro';
// import { SwipeCell as VantSwipeCell } from '@antmjs/vantui';
import { Swipe, Button } from '@nutui/nutui-react-taro';

// 兼容原SwipeCell的props
interface SwipeCellProps {
  children: React.ReactNode;
  renderRight?: React.ReactNode;
  rightWidth?: number;
  className?: string;
  onClose?: () => void;
  style?: React.CSSProperties;
}

const SwipeCell = forwardRef<any, SwipeCellProps>((props, ref) => {
  // const vantRef = useRef<any>();
  const nutRef = useRef<any>();

  useImperativeHandle(ref, () => ({
    close: () => {
      // 只保留 nutRef 相关逻辑
      if (nutRef.current) {
        nutRef.current.close && nutRef.current.close();
      }
    }
  }));

  // 只保留 H5 端用NutUI
  let rightAction = props.renderRight;
  if (!rightAction) {
    rightAction = (
      <Button
        size="small"
        style={{ background: '#4F66FF', color: '#fff' }}
        onClick={props.onClose}
      >
        操作
      </Button>
    );
  }
  return (
    <Swipe
      ref={nutRef}
      rightAction={rightAction}
      className={props.className}
      style={props.style}
      onActionClick={props.onClose}
    >
      {props.children}
    </Swipe>
  );
});

export default SwipeCell; 