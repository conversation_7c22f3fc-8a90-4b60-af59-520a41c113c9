// @import '@/assets/styles/var.less';
.page {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(180deg, #dee6fd 20px, #fff 40%);
  padding-bottom: 250px;
  -webkit-overflow-scrolling: touch;

}

.scrollViewWrap {}

.item-del-icon {
  width: 80px;
  height: 80px;
}

.item-del-icon-confirm {
  height: 80px;
  // padding: 0 20px;
  border-radius: 40px;
  width: 220px;
  text-align: center;
  margin: 0 16px;
}

.repeatListbody {
  max-height: 60vh;
  overflow: auto;
}

.empty-pic {
  width: 532px;
}

.empty-title {
  margin-top: -20%;
}

.emptyWrap {
  // padding-bottom: 150px;
}

//类型列表
.type-list {

  // width:88px;
  .type-pic {
    width: 58px;
  }
}

// 文件列表
.list-wrap {}

.list-item-main {
  box-shadow: 0px 8px 20px 0px rgba(#c2cdf0, 20%);
}

.delIcon {
  width: 42px;
  height: 42px;
}

.fixFootBox {
  height: 200px;
  box-sizing: border-box;
}

.imgPopup {
  width: 100%;
}

.previewImg {
  max-width: 100%;
  max-height: 100%;

}

.input {
  width: 100%;
  background-color: #f1f2f3;
  border: 1px solid #f1f2f3;
  height: 88px;
  display: flex;
  align-items: center;
  border-radius: 16px;
  padding: 0 24px;
}

.placeholder {
  display: flex;
  align-items: center;
}


:global {
  .nut-swipe{
    background-color: #ffffff00 !important;
  }
  

}