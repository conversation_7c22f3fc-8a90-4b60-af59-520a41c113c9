/*
 * @Author: 范非苗
 * @Date: 2025-04-15 16:08:29
 * @LastEditTime: 2025-04-23 13:32:07
 */
import Taro from '@tarojs/taro'
import { View, Image } from '@tarojs/components'
import styles from './ListItem.module.less'

function Index({
  index = 0,
  item = { name: '', size: '', status: '' },
  renderRight,
  className = 'bg-white',
  iconbgClassName = 'bg-white',
  onClick = () => {}
}) {
  //文件类型对应的icon
  function formatFileIcon(name: string = '') {
    if (!name) return ''
    const extension = name.split('.').pop()?.toLowerCase() || ''
    switch (extension) {
      case 'jpeg':
      case 'jpg':
        return require('@/assets/images/<EMAIL>')
      case 'png':
        return require('@/assets/images/<EMAIL>')
      case 'heic':
        return require('@/assets/images/<EMAIL>')
      case 'pdf':
        return require('@/assets/images/<EMAIL>')
      case 'doc':
      case 'docx':
        return require('@/assets/images/<EMAIL>')
      case 'xls':
      case 'xlsx':
        return require('@/assets/images/<EMAIL>')
      case 'ppt':
      case 'pptx':
        return require('@/assets/images/<EMAIL>')
      case 'txt':
        return require('@/assets/images/<EMAIL>')
      default:
        return require('@/assets/images/<EMAIL>')
    }
  }

  return (
    <>
      <View className={`flex align-center  p-base rounded   ${className}`} onClick={onClick}>
        <View
          className={`mr-base flex-shrink flex align-center justify-center ${styles['item-thumb-box']} rounded ${iconbgClassName}`}
        >
          <Image
            className="width-100 height-100"
            mode="aspectFit"
            src={formatFileIcon(item.name)}
          />
        </View>
        <View className="flex-1">
          <View className="mb-1 text-first font-s lineh-1-2">{item.name}</View>
          <View className=" font-xs text-fourth flex align-center">
            <View className="mr-base">{(item.size / 1024).toFixed(2)}KB</View>
            <View className="text-status-primary">{item.status}</View>
          </View>
        </View>
        <View className="flex-shrink">{renderRight}</View>
      </View>
    </>
  )
}
export default Index
