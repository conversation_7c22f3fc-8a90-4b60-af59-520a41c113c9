/*
 * @Author: 范非苗
 * @Date: 2025-04-29 09:59:51
 * @LastEditTime: 2025-05-03 08:24:05
 *  存储登录信息返回的相关数据
 */
import Taro from '@tarojs/taro'
import { createSlice } from '@reduxjs/toolkit'

let _loginResult = {}
let _token = ''
let _appId = ''
let _wxname = ''

if (Taro.getStorageSync('loginResult')) {
  _loginResult = Taro.getStorageSync('loginResult')
  _token = _loginResult?.token
  _appId = _loginResult?.appId
  _wxname = _loginResult?.wxname
}

const loginResultSlice = createSlice({
  name: 'loginResult', // 命名空间
  initialState: {
    // 初始化数据
    loginResult: _loginResult, //登陆返回的数据
    token: _token, // token
    appId: _appId, // 小程序appid
    wxname: _wxname // 从哪个小程序跳转过来
  },
  reducers: {
    // 函数方法
    // 存储登录信息返回的相关数据
    saveLoginResult(state, action) {
      state.loginResult = action.payload
      state.token = state.loginResult?.token
      state.appId = state.loginResult?.appId
      state.wxname = state.loginResult?.wxname
      console.log('2', state.loginResult)
      Taro.setStorageSync('loginResult', state.loginResult)
      Taro.setStorageSync('token', state.token)
    },
    // 清空登录信息返回的相关数据
    clearLoginResult(state) {
      state.loginResult = {}
      state.token = ''
      state.appId = ''
      state.wxname = ''
      Taro.clearStorageSync()
      Taro.clearStorage()
    }
  }
})

export const { saveLoginResult, clearLoginResult } = loginResultSlice.actions
export default loginResultSlice.reducer
