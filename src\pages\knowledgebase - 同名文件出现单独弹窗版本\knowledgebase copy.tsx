/*
 * @Author: 范非苗
 * @Date: 2025-04-21 14:39:04
 * @LastEditTime: 2025-04-21 14:39:07
 */
/*
 * @Author: 范非苗
 * @Date: 2025-04-12 19:13:13
 * @LastEditTime: 2025-04-21 14:38:02
 */

import { View, Image, Button } from '@tarojs/components'
import Taro, { useDidShow } from '@tarojs/taro'
import { Popup, Icon, SwipeCell, ISwiperCellInstance } from '@antmjs/vantui'
import { useState, useEffect, useRef } from 'react'
import Popover from '@/components/common/Popover/Popover'
import ListItem from './component/ListItem/ListItem'
import dayjs from 'dayjs'
import wx from 'weixin-js-sdk'
import styles from './knowledgebase.module.less'

//TS 定义
//定义uploadTypes类型
interface UploadType {
  title: string
  value: string
  iconurl: string
}
//
// 包装后的FileList
interface FormatUploadedFile {
  file: File
  name: string
  type: string
  size: number
  path?: string
  length?: number
  errMsg?: string
  confirmDelete?: boolean
}
export default function Index() {
  /*变量声明*/
  const uploadTypes: UploadType[] = [
    { title: '微信上传', value: 'weixin', iconurl: require('@/assets/images/<EMAIL>') },
    { title: '手机文件上传', value: 'file', iconurl: require('@/assets/images/<EMAIL>') }
  ] //上传方式
  const [uploadTypesPopup, setUploadTypesPopup] = useState(false) // 上传类型弹窗
  const [uploadedList, setUploadedList] = useState<FormatUploadedFile[]>([]) // 已经上传的文件队列
  const [repeatFiles, setRepeatFiles] = useState<FormatUploadedFile[]>([]) // 重复文件列表
  const [repeatFilesPopup, setRepeatFilesPopup] = useState(false) // 文件重复弹窗
  const [substandardList, setSubstandardList] = useState<FormatUploadedFile[]>([]) // 不符合标准文件列表
  const [substandardPopup, setSubstandardPopup] = useState(false) // 不符合标准文件弹窗
  const swipcellRefs = useRef<any>([]) // 存储所有输入框的ref

  //函数
  // push 文件到文件队列里面
  function handlePushFile(file: File) {
    if (uploadedList.length === 0) {
      _setUploadedList(file)
    } else {
      const index = uploadedList.findIndex((item) => item.name === file.name)
      if (index >= 0) {
        setRepeatFiles((prevList1) => {
          const obj = {
            file: file,
            type: file.type,
            size: file.size,
            name: file.name,
            errMsg: '',
            confirmDelete: false
          }
          const arr = [...prevList1, obj]
          // 打开重复提醒弹窗，写在这里的原因是，react 不支持异步更新 state，我在循环外面打印repeatFiles 依旧是旧值，
          // 而我写在这里打开弹窗，react 也不会一会就打开一个，一会就打开一个，他都是攒到一起一次性更新的
          if (arr.length > 0) {
            onToggleRepeatFilesTc(true)
          }
          return arr
        })
      } else {
        _setUploadedList(file)
      }
    }
  }
  // 设置已经上传的文件列表
  function _setUploadedList(file: File, callback = () => {}) {
    setUploadedList((prevList) => {
      const obj = {
        file: file,
        type: file.type,
        size: file.size,
        name: file.name,
        errMsg: '',
        confirmDelete: false
      }
      if (typeof callback === 'function') {
        callback()
      }
      return [...prevList, obj]
    })
  }
  // 设置不符合标准的文件列表
  function _setSubstandardList(file: File, errMsg: '', callback = () => {}) {
    setSubstandardList((prevList) => {
      const obj = {
        file: file,
        type: file.type,
        size: file.size,
        name: file.name,
        errMsg: errMsg
      }
      const arr = [...prevList, obj]
      if (arr.length > 0) {
        onToggleSubstandardTc(true)
      }
      if (typeof callback === 'function') {
        callback()
      }
      return arr
    })
  }

  // 上传弹窗开关
  function handleToggleUploadTc(visible: boolean = false) {
    setUploadTypesPopup(visible)
  }
  // 重复提醒弹窗开关
  function onToggleRepeatFilesTc(visible: boolean = false) {
    setRepeatFilesPopup(visible)
  }
  // 格式不符合弹窗开关
  function onToggleSubstandardTc(visible: boolean = false) {
    setSubstandardPopup(visible)
  }
  // input上传文件校验类型和大小
  function onValidateFiles(FileList: File[] = [], type: string = 'system') {
    const maxFiles = 10 // 一次最多上传10份
    const fileLength = FileList.length
    if (fileLength > maxFiles) {
      Taro.showToast({
        title: `已达选择文件上限(一次最多上传${maxFiles}份),您选择了${fileLength}份,请重新选择`,
        icon: 'none'
      })
      return false
    }
    const allowedImageTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/heic'] // 图片类型
    const allowedOtherTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain'
    ] // 文件类型
    const allowedImageExtensions = ['jpg', 'jpeg', 'png', 'heic'] // 图片扩展名
    const allowedOtherExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'] // 其他文件扩展名
    const maxImageSize = 10 * 1024 * 1024 // 图片最大尺寸10MB  10 * 1024 * 1024
    const maxOtherSize = 50 * 1024 * 1024 // 其他文件最大尺寸50MB 50 * 1024 * 1024
    for (let i = 0; i < fileLength; i++) {
      const file = FileList[i] // 获取单张上传的文件
      const type = file.type // 文件类型
      const size = file.size // 文件大小
      const extension = file.name.split('.').pop()?.toLowerCase() || '' // 文件扩展名
      const isImage = allowedImageExtensions.includes(extension) // 是否是图片
      const maxSize = isImage ? maxImageSize : maxOtherSize // 允许的最大上传尺寸
      console.log('file', file)
      console.log('size', size)
      console.log('extension', extension)
      if (!file) {
        continue
      }
      // 验证扩展名以及文件类型(防止用户通过修改扩展名进行欺骗上传)
      if (
        [...allowedImageExtensions, ...allowedOtherExtensions].includes(extension) &&
        [...allowedImageTypes, ...allowedOtherTypes].includes(type)
      ) {
        // 验证大小
        if (size > maxSize) {
          _setSubstandardList(file, '文件大小超出限制')
          continue
        } else {
          handlePushFile(file)
        }
      } else {
        _setSubstandardList(file, '文件格式不支持')
        continue
      }
    }
    handleToggleUploadTc(false)
    //移除input(type='file')节点
    document.getElementById('fileInput')?.remove()
  }

  // 选择文件上传方式
  function handleUploadTypeChange(type: string) {
    switch (type) {
      case 'weixin':
        if (window.__wxjs_environment === 'miniprogram') {
          console.log('h5告知小程序事件')
          // 向小程序发送消息
          // wx.miniProgram.postMessage({
          //   data: {
          //     action: 'chooseFile'
          //   }
          // })
          // wx.miniProgram.navigateBack()
          wx.miniProgram.navigateTo({
            url: '/pages/eman/uploadFile/index'
          })
        } else {
          Taro.showToast({
            title: '请使用微信小程序进行上传',
            icon: 'none'
          })
        }
        // wx.miniProgram.navigateTo({
        //   url: '/pages/eman/home/<USER>'
        // })

        break
      case 'file':
        const input = document.createElement('input')
        input.id = 'fileInput'
        input.type = 'file'
        input.multiple = true
        input.name = 'myFiles[]'
        // input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png'
        input.addEventListener('change', handleFileInputChange)
        document.body.appendChild(input)
        input.click()
        break
        break
    }
  }
  function handleFileInputChange(e) {
    e.stopPropagation() // 阻止冒泡
    // const FileList = e.target.files //获取文件上传队列
    onValidateFiles(e.target.files, 'system')
  }
  // 删除询问
  function onIsConfirmDelete(index) {
    setUploadedList((prevList) => {
      const newList = prevList.map((file, i) => {
        if (i === index) {
          return { ...file, confirmDelete: true }
        }
        return file
      })
      return newList
    })
  }
  // 确认删除
  function onDelete(index, callback = () => {}) {
    setUploadedList((prevList) => {
      const newList = prevList.filter((item, index2) => {
        if (index2 !== index) {
          console.log('item' + index2, item)
          return { ...item, confirmDelete: false }
        }
      })
      console.log('newList', newList)
      //return prevList.filter((file, index2) => index2 !== index)
      return newList
    })
    Taro.showToast({ title: '删除成功', icon: 'success', duration: 3000 })
  }
  // 替换函数
  function handleReplace() {
    // 计算出同名数组
    uploadedList.forEach((item, index, array) => {
      repeatFiles.forEach((item2, index2) => {
        if (item.name == item2.name) {
          array[index] = item2
        }
      })
    })
    // 替换
    setUploadedList(uploadedList)
    // 清除同名列表
    setRepeatFiles([])
    //关闭弹窗
    onToggleRepeatFilesTc(false)
  }
  //全部保留
  function handleSaveAsOtherName() {
    const arr = repeatFiles.map((item, index) => {
      const timestamp = dayjs().format('YYYYMMDDhhmmss')
      const lastDotIndex = item.name.lastIndexOf('.')
      const name1 = item.name.substring(0, lastDotIndex)
      const extension = item.name.substring(lastDotIndex)
      const reName = `${name1}-${timestamp}${extension}`
      const newItem = new File([item], reName, { type: item.type }) // 重新创建一个文件类型
      return newItem
    })
    // push 到已上传列表
    setUploadedList([...uploadedList, ...arr])
    // 清除同名列表
    setRepeatFiles([])
    //关闭弹窗
    onToggleRepeatFilesTc(false)
  }
  // 接口：重试上传
  function handleRetryUploadApi() {
    Taro.showToast({
      title: '重试上传',
      icon: 'none'
    })
  }
  // 重新上传
  function handleReUpload() {
    setSubstandardList([])
    onToggleSubstandardTc(false)
    handleToggleUploadTc(true)
  }

  // 钩子函数
  useEffect(() => {
    console.log('useEffect')
  }, [uploadedList])

  //  页面显示
  useDidShow(() => {
    console.log('数据渲染', styles)

    // wx.miniProgram.onMessage((data) => {
    //   console.log('从uploadFile回来的数据', data)
    // })
  })
  return (
    <View className={`${uploadedList.length == 0 ? 'bg-white' : styles.page}`}>
      <View className="flex flex-column mheight-100vh pb-5 box-border">
        {uploadedList.length == 0 ? (
          <View
            className={`text-center pb-5 flex-1 flex flex-column justify-center ${styles.emptyWrap}`}
          >
            <View>
              <Image
                className={styles['empty-pic']}
                mode="aspectFit"
                src={require('@/assets/images/<EMAIL>')}
              />
            </View>
            <View className={`font-weight-bold font mb-1 ${styles['empty-title']}`}>
              暂无知识库
            </View>
            <View className="font-xs text-second">
              <View>上传资料到知识库后，E人会有限从知识库检索信息。</View>
              <View>上传独有心得可以让我拥有更深层的智慧</View>
            </View>
          </View>
        ) : (
          <View className="flex-1 list-wrap pt-base">
            {uploadedList.map((item, index) => {
              return (
                <SwipeCell
                  // ref={(el) => {
                  //   if (el) {
                  //     swipcellRefs.current[index] = el
                  //   }
                  // }}
                  key={index}
                  asyncClose
                  onClose={() => {}}
                  className="position-relative"
                  renderRight={
                    <View
                      style={{ transform: `${item.confirmDelete ? 'translateX(-72px)' : ''}` }}
                      className={`flex align-center  height-100 pr-base position-relative`}
                    >
                      {!item.confirmDelete ? (
                        <View
                          className={`flex align-center justify-center bg-danger position-absolute  rounded-circle ${styles['item-del-icon']}`}
                          hoverClass="bg-black"
                          onClick={() => onIsConfirmDelete(index)}
                        >
                          <Icon name="delete-o" size="22px" color="#fff" />
                        </View>
                      ) : (
                        <View
                          className={`flex align-center justify-center bg-danger position-absolute  ${styles['item-del-icon-confirm']}`}
                          hoverClass="bg-black"
                          onClick={() => {
                            onDelete(index)
                          }}
                        >
                          <Icon name="delete-o" size="22px" color="#fff" />
                          <View className="text-white ml">确认删除</View>
                        </View>
                      )}
                    </View>
                  }
                >
                  <View
                    className={`pl-base mb-base`}
                    style={{ paddingRight: `${item.confirmDelete ? '76px' : '16px'}` }}
                  >
                    <ListItem
                      item={item}
                      index={index}
                      className={`bg-white ${styles['list-item-main']} `}
                      iconbgClassName="bg-light-muted"
                      renderRight={
                        <View
                          className="ml-base flex-shrink text-primary bg-tag-primary item-tag py-1  px-3 rounded-xxm font-xs"
                          onClick={handleRetryUploadApi}
                        >
                          重试
                        </View>
                      }
                    ></ListItem>
                  </View>
                </SwipeCell>
              )
            })}
          </View>
        )}
        <View className="p-base flex justify-center  flex-shrink">
          <Button
            className="btn-theme-brand-default"
            round={false}
            onClick={() => handleToggleUploadTc(true)}
          >
            上传资料
          </Button>
        </View>
      </View>
      {/* 上传类型选择 */}
      <Popup
        show={uploadTypesPopup}
        position="bottom"
        round
        style={{
          overflow: 'visible' // 关键：允许内容溢出
        }}
        onClose={() => handleToggleUploadTc(false)}
      >
        <View className="p-base py-3  pb-6">
          <View className="flex width-100 align-center mb-6 mt-1 pl-4">
            <View className="flex-1 text-center">
              <View className="font-weight-bold font flex align-center justify-center">
                请选择上传方式
                <Popover
                  content={
                    <>
                      <View className="font-weight-bold text-center font-s mb-1">上传限制</View>
                      <View className="font-xs text-fourth">
                        文件上传限制：仅支持PDF、Word文档(docx、doc)、Excel表格(xlsx、xls)、PPT文件(PPT、PPTX)、TXT、JPG、JPEG、PNG、HEIC格式。图片文件单个大小不超过10MB，其他文件单个大小不超过50MB。
                      </View>
                    </>
                  }
                >
                  <Icon name="question-o" className="mt" size="18px" />
                </Popover>
              </View>
            </View>
            <View className="flex-shrink">
              <Icon
                name="cross"
                className="text-second"
                size="22px"
                onClick={() => handleToggleUploadTc(false)}
              />
            </View>
          </View>
          <View className={`width-100 ${styles['type-list']}`}>
            {uploadTypes.map((item, index) => {
              return (
                <View
                  key={index}
                  className="p-base mb-base bg-light flex align-center position-relative rounded width-100"
                  onClick={() => {
                    handleUploadTypeChange(item.value)
                  }}
                >
                  <Image
                    className={` flex-shrink mr-base m-0 ${styles['type-pic']}`}
                    mode="aspectFit"
                    src={item.iconurl}
                  />
                  <View className="flex-1">{item.title}</View>
                  <View className="flex-shrink flex align-center">
                    {item.value === 'weixin' && (
                      <View className="flex align-center flex-shrink">
                        <View className="mr-base type-hint font-xs bg-tag-primary rounded text-primary p-1 px-2">
                          推荐
                        </View>
                        <Icon name="arrow" className="text-fourth" size="18px" />
                      </View>
                    )}
                  </View>
                </View>
              )
            })}
          </View>
        </View>
      </Popup>
      {/* 文件重复提醒 */}
      <Popup
        show={repeatFilesPopup}
        round
        position="bottom"
        onClose={() => onToggleRepeatFilesTc(false)}
      >
        <View className="p-base">
          <View className="flex width-100 pl-4">
            <View className="flex-1 text-center mb-base">
              <View className="font-weight-bold font-m mb-base">同名文件</View>
              <View className="font-xs text-fourth">
                该知识库存在以下同名文件。若替换，则会覆盖其当前内容；若保留，则同时留存新旧文件。
              </View>
            </View>
            <View className="flex-shrink">
              <Icon name="cross" size="24px" onClick={() => onToggleRepeatFilesTc(false)} />
            </View>
          </View>
          <View className={styles.repeatListbody}>
            {repeatFiles.map((item, index) => {
              return (
                <ListItem
                  item={item}
                  index={index}
                  key={index}
                  className="bg-light mb-base"
                ></ListItem>
              )
            })}
          </View>
          <View className="flex align-center justify-evenly">
            <Button className=" btn-gray-default span-9 mb-0" onClick={handleReplace}>
              替换
            </Button>
            <Button className="btn-theme-brand-default span-9 mb-0" onClick={handleSaveAsOtherName}>
              全部保留
            </Button>
          </View>
        </View>
      </Popup>
      {/* 不符合标准文件提醒 */}
      <Popup
        show={substandardPopup}
        round
        position="bottom"
        onClose={() => onToggleSubstandardTc(false)}
      >
        <View className="p-base">
          <View className="flex width-100">
            <View className="flex-1 text-center mb-base pl-4">
              <View className="font-weight-bold font-m mb-base">文件上传错误</View>
              <View className="font-xs text-fourth">以下文件格式或大小不符合要求，请重新上传</View>
            </View>
            <View className="flex-shrink">
              <Icon name="cross" size="24px" onClick={() => onToggleSubstandardTc(false)} />
            </View>
          </View>
          <View className="repeatListbody">
            {substandardList.map((item, index) => {
              return (
                <ListItem
                  item={item}
                  index={index}
                  key={index}
                  className="bg-light mb-base"
                ></ListItem>
              )
            })}
          </View>
          <View className="flex justify-center">
            <Button className="btn-theme-brand-default" onClick={handleReUpload}>
              重新上传
            </Button>
          </View>
        </View>
      </Popup>
    </View>
  )
}
