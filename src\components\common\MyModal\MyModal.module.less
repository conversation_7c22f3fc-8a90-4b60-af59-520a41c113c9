@basepad: 32px;
@first: #333;

.tcWrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 655;
  background-color: rgba(#000, 0.5);
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  padding: 64px;
  width: 100%;
  height: 100vh;
}

.tcMain {
  background-color: #fff;
  border-radius: 24px;
  padding: @basepad;
  width: 100%;
}

.tcTitleWrap {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: @basepad;
  position: relative;
  padding: 0 50px;
}

.tcTitle {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: @first;
  font-weight: bold;
}

.tcClose {
  position: absolute;
  right: 0;
  width: 56px;
  height: 56px;
  border-radius: 100%;
  background-color: rgba(#000, 0.3);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 0;
}

.tcContentWrap {
  width: 100%;
  margin-bottom: @basepad;
}

.btnWrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

}

.btnBox {
  width: 49%;
}

.btnCancel {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 88px;
  height: 88px;
  background-color: #eee;
  color: #777;
  font-size: 28px;

  &:after {
    content: "";
    display: none;
  }
}

.btnSubmit {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88px;
  border-radius: 88px;
  background: #1989FA;
  color: #fff;
  font-size: 28px;


  &:after {
    content: "";
    display: none;
  }

}
