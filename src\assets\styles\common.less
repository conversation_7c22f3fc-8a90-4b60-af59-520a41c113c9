@import "./var.less" ;


* {
  padding: 0;
  margin: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html,
body {
  min-height: 100%;
  font-size: 100%;
  overflow: auto;
}

body {
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif,
    '宋体';
  word-wrap: break-word;
  word-break: break-all;
}

header,
nav,
main,
footer,
section,
article,
aside,
figure,
figcaption {
  display: block;
}

h1,
h2,
h3,
h4,
h5,
h6,
i,
b,
em {
  font-weight: normal;
  font-style: normal;
}

ul,
ol {
  list-style-type: none;
}

ins {
  text-decoration: none;
}

a {
  color: inherit;
  text-decoration: none;
}

a.txt:hover {
  text-decoration: underline;
}

img {
  max-width: 100%;
  max-height: 100%;
}

/* 透明按钮 */
.radio-transparent {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
  opacity: 0;
  filter: opacity(0%);
}

/**/
.clearfix {
  &:before,
  &:after {
    content: '';
    display: table;
  }
  &:after {
    clear: both;
  }
}
.scrollbar-primary {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  &::-webkit-scrollbar-track {
    background-color: #c2bfbf;
    border-radius: 2px;
    box-shadow: inset 0 0 6px #ccc;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #eee;
    -webkit-box-shadow: inset 0 0 6px #dfdfe3;
    box-shadow: inset 0 0 6px #dfdfe3;
  }
  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}

.placeholder-inherit {
  &::-webkit-input-placeholder {
    color: inherit;
  }

  &:-moz-placeholder {
    color: inherit;
  }

  &::-moz-placeholder {
    color: inherit;
  }

  &:-ms-input-placeholder {
    color: inherit;
  }
}

// 去除input number 的外观
.input-number-noappearance {
  -moz-appearance: textfield;
  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type='number'] {
    -moz-appearance: textfield;
  }
  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}
.btn-default-hook{
  @height:88px;
  height:@height;
  border-radius: 50px;
  font-size: 32px;
  color:@white;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
  box-shadow: none;
  margin:0;
 &:after{
  content: '';
  border: none !important;
 }

}
.btn-gray-default{
  background: #f6f6f6;
  color:#777;
  &:extend(.btn-default-hook);
  &.van-button{
    &:extend(.btn-default-hook);
  }
  &:after{
    content: '';
    border: none !important;
   }
}
.btn-theme-brand-default{
  background: linear-gradient(90deg, @theme-brand 20%, @theme-brand-muted 100%);
  &:extend(.btn-default-hook);
  &.van-button{
    &:extend(.btn-default-hook);
  }
  &:after{
    content: '';
    border: none !important;
   }
}
// taro-button-core:after {
//   content: '';
//   display: none;
//   line-height: 0;
//   border: none !important;
// }
