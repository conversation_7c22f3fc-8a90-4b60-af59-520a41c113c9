/*
 * @Author: 范非苗
 * @Date: 2025-04-23 13:45:29
 * @LastEditTime: 2025-04-29 09:21:56
 */
// 获取文件扩展名,转小写
export const getFileExt = (fileName: string = '') => {
  const res = fileName.split('.').pop()?.toLowerCase() || ''
  return res
}
// 获取window.href 参数
export const getUrlParam = (name: string = '', native: boolean = false) => {
  const href = window.location.href
  const index = href.indexOf('?')
  const search = href.slice(index + 1)
  const queries = search.split('&')
  const params = {}
  queries.forEach((query) => {
    const [key, value] = query.split('=')
    params[key] = decodeURIComponent(value)
  })
  return native ? params : params[name]
}
// 获取数据加载状态
export const getStatus= ({total,page,pageSize}={total:0,page:0,pageSize:0})=>{
  pageSize=pageSize||10
  let loadStatus=0
  if (total===0) {
    loadStatus=0
  }else if (page*pageSize >= total) {
    loadStatus=3
  }else{
    loadStatus=1  // 又显示回 上拉加载更多
  }
  return loadStatus
}
