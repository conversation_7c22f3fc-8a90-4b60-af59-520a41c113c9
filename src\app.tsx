/*
 * @Author: 范非苗
 * @Date: 2025-04-12 19:13:13
 * @LastEditTime: 2025-05-01 19:13:55
 */

import { useLaunch, useRouter } from '@tarojs/taro'
import { Provider } from 'react-redux'
import store from '@/store' // 确保你的 store 文件路径正确
import { saveLoginResult } from '@/store/loginResult'
import request from '@/service/request'

import VConsole from 'vconsole';
import { CORRECT_CODE } from "@/service/config"

import './app.less'

function App({ children }) {
  const router = useRouter()
  const params = router.params
  global.$R = request
  global.CORRECT_CODE = CORRECT_CODE
  store.dispatch(saveLoginResult(params)) // 存到store
  useLaunch(() => {
    // 添加vConsole
    if (process.env.TARO_APP_MODE !== 'production' || params.debug === 'true') {
      const vConsole = new VConsole();
    }
  })

  // children 是将要会渲染的页面
  return <Provider store={store}>{children}</Provider>
}

export default App
