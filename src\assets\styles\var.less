// 主题色
// @primaryColor: var(--primary-color, #6742FF);
/* 主色调 */
@primary: #174CFA;
/*蓝色*/
@primary-muted: mix(#ff0000, @primary, 50%);
@primary-light: lighten(@primary, 10%);
@theme-brand: #3D83FF;
@theme-brand-muted: #6742FF;

/*主题色*/
/*
重试 #4F66FF;
等待上传 #3D66E4
*/

@success: #06C394;
/*绿色*/
@success-muted: darken(@success, 20%);
/*绿色*/
@success-light: lighten(@success, 20%);
/*绿色*/
@danger: #F75A44;
/*红色*/
@danger-muted: darken(@danger, 20%);
/*红色*/
@danger-light: lighten(@danger, 20%);
/*红色*/
@warning: #FF6A55;
/*黄色*/
@warning-muted: darken(@warning, 20%);
/*黄色*/
@warning-light: lighten(@warning, 20%);
/*黄色*/
@info: #FF912A;
/*详情*/
@light: #F6F6F6;
/*填充：浅色/默认，跟白色无差的灰色,用于背景*/
// @light-muted:mix (#000,@light,10%);
@light-muted: #f7f7f7;
/*填充深色*/
@avatar:#d9d9d9;  // 头像填充色
@icon-primary:#5D5D5D; // icon 按钮背景色
@doc-theme-brand:#5D5D5D; // 按钮背景色
/*状态/进度颜色*/
@status-warning:#FF6A55;
@status-primary:#3D66E4;
@status-fail:#FF6A55;
@status-hint:#FF912A;
//tag 背景色
@tag-primary:#D3E2FF ;


@red: red;
/*红色*/
@black: #000;
/*黑色*/
@white: #fff;
/*白色*/
/*字体颜色*/
@first: #272C47;
/*字体高强调*/
@second: #67686F;
/*灰色*/
@third: #9597A0;
/*灰色 用于副标题 备注这种*/
@fourth: #c1c6c6;
/*灰色 用于副标题 备注这种*/
/*边框颜色*/
@borderColor: #ededed;
/*边框浅灰色*/
@borderColor-muted: #e0e0e0;
/*边框深灰色*/
/*form*/
@disabled: #c1c6c6;
@colorInput: #808080;

/*对应鼠标按下去的颜色*/
@hoverPrimary: #ebebeb;

/*以下为兼容历史，尽量不用，只保留*/
/*字体色调*/
// 强调/正文标题
@textColor-1: #323233;
// 次强调/正文标题
@textColor-2: #646566;
// 次要信息
@textColor-3: #969799;
// 置灰信息
@textColor-4: #c1c6c6;
// 线条/深
@textColor-5: #e0e0e0;
// 线条/输入框描边
@textColor-6: #ededed;
// 线条/浅
@textColor-7: #f0f0f0;
// 填充/深
@textColor-8: #f7f7f7;
// 填充/浅
@textColor-9: #fafafa;


