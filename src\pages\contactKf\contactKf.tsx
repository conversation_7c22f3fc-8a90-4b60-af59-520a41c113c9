import React, { useEffect } from 'react'
import { View,Image } from '@tarojs/components'
import Taro,{ useDidShow} from '@tarojs/taro'
import styles from './contactKf.module.less'
function Index() {
  // 对应 onShow
  useDidShow(() => {
    Taro.setNavigationBarTitle({
      title: '联系客服'
    })
  })
  return (
    <>
      <View className={`mheight-100vh  bg-light flex flex-column align-center justify-center p-5 box-border `}>
        <View className={`${styles.codeBox} position-relative`}>
           <View className={`position-absolute top-0 right-0 left-0 bottom-0  rounded-m zindex-2 ${styles.codeBoxMain} btg-white`}>
             <Image  className={`width-100 height-100`} mode="aspectFit" src={require('@/assets/images/<EMAIL>')}></Image>
             <View className={`position-absolute zindex-3 ${styles.codeImgBox}`}>
              <Image  className={`width-100 height-100`} mode="aspectFit" src={require('@/assets/images/<EMAIL>')}></Image>
           </View>
           </View>
        </View>
      </View>
    </>
  )
}

export default Index
