/*
 * @Author: 范非苗
 * @Date: 2025-04-12 19:13:12
 * @LastEditTime: 2025-04-12 20:14:37
 */
import eslintPluginPrettier from 'eslint-plugin-prettier'

module.exports = {
  extends: [
    'taro/react',
    'eslint:recommended',
    'plugin:prettier/recommended', // 使用这个插件来让ESLint遵守Prettier的规则
  ],
  rules: {
    'react/jsx-uses-react': 'off',
    'react/react-in-jsx-scope': 'off',
  },
  eslintPluginPrettier,
}
