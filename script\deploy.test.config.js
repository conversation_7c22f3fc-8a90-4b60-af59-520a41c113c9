// 部署配置，自己根据项目情况，自定义配置

const config = {
  distPath: 'dist', // 本地打包文件目录 必填
  script: 'pnpm run testbuild:yuanyi', // 项目打包命令
  delDistFile: true, // 打包后删除打包文件
  serverConfig: [
    {
      // host: '**************', // 服务器地址 必填
      host: '**************', // 服务器地址 必填
      port: '27', // 端口 默认 22
      username: 'ubuntu', // 登录服务器用户名 必填
      privateKey: '~/.ssh/id_rsa', // 登录服务器密钥
      // passphrase: 'xxx', // 密钥密码
      webDir: '/opt/quantum/quantum-h5'
    }
  ]
}

module.exports = config
