.page {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(180deg, #dee6fd 20px, #fff 40%);
  padding-bottom: 250px;
  -webkit-overflow-scrolling: touch;
}
.item-del-icon {
  width: 80px;
  height: 80px;
}
.item-del-icon-confirm {
  height: 80px;
  border-radius: 40px;
  width: 220px;
  text-align: center;
  margin: 0 16px;
}
.repeatListbody {
  max-height: 60vh;
  overflow: auto;
}
.empty-pic {
  width: 532px;
}
.empty-title {
  margin-top: -20%;
}
.type-list .type-pic {
  width: 58px;
}
.list-item-main {
  box-shadow: 0px 8px 20px 0px rgba(194, 205, 240, 0.2);
}
.delIcon {
  width: 42px;
  height: 42px;
}
.fixFootBox {
  height: 200px;
  box-sizing: border-box;
}
.imgPopup {
  width: 100%;
}
.previewImg {
  max-width: 100%;
  max-height: 100%;
}
.input {
  width: 100%;
  background-color: #f1f2f3;
  border: 1px solid #f1f2f3;
  height: 88px;
  display: flex;
  align-items: center;
  border-radius: 16px;
  padding: 0 24px;
}
.placeholder {
  display: flex;
  align-items: center;
}
