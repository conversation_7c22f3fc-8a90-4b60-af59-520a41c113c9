/*
 * @Author: 范非苗
 * @Date: 2025-04-12 19:13:13
 * @LastEditTime: 2025-05-03 12:14:17
 */

import { View, Image, Button, Input, ScrollView } from '@tarojs/components'
import Taro, { useDidShow, usePullDownRefresh, useReachBottom } from '@tarojs/taro'
import { Popup, Icon, SwipeCell, Dialog } from '@antmjs/vantui'
import { useState, useEffect, useRef } from 'react'
import ListItem from './component/ListItem/ListItem'
import Loadmore from '@/components/common/Loadmore/Loadmore'
import MyModal from '@/components/common/MyModal/MyModal'
import wx from 'weixin-js-sdk'
import {
  IMAGE_TYPES,
  OTHER_TYPES,
  IMAGE_EXTENSIONS,
  OTHER_EXTENSIONS,
  MAXSIZE_IMAGE,
  MAXSIZE_OTHER
} from '@/config/var'
import { getFileExt, getStatus } from '@/utils/common'
import { useSelector } from 'react-redux'
import { apiMap } from '@/service/api'
import SwipeCellVant from './component/SwipeCell' // 引入自定义SwipeCell

import styles from './knowledgebase.module.less'

// 页面配置 - 禁止左滑返回
definePageConfig({
  navigationBarTitleText: '知识库',
  enablePullDownRefresh: true,
  disableSwipeBack: true, // 禁止左滑返回
})

const PAGE_SIZE = 10
const IOCN_EDIT = 'https://assets.ecaisys.com/2025/6/4/8L3DWTXHZB/7/a96744486114598a3a0f020c4fe2f0c0.png'

//TS 定义
//定义uploadTypes类型
interface UploadType {
  title: string
  value: string
  iconurl: string
}

// 包装后的File
interface FormatUploadedFile {
  file: File
  name: string
  type: string
  size: number
  path?: string
  length?: number
  errorInfo?: string | null
  id?: string
  "url"?: string
  "uploadTime"?: string
  "status"?: number
  _status?: string | null
}

const Dialog_ = Dialog.createOnlyDialog()
function Index() {
  /*变量声明*/
  const uploadTypes: UploadType[] = [
    { title: '微信上传', value: 'weixin', iconurl: require('@/assets/images/<EMAIL>') },
    { title: '手机文件上传', value: 'file', iconurl: require('@/assets/images/<EMAIL>') }
  ] //上传方式
  const [uploadTypesPopup, setUploadTypesPopup] = useState(false) // 上传类型弹窗
  const [substandardPopup, setSubstandardPopup] = useState(false) // 不符合标准文件弹窗
  const [previewTc, setPreviewTc] = useState(false) // 弹窗:预览
  const [showEditTc, setShowEditTc] = useState(false) // 弹窗:编辑
  const [fileName, setFileName] = useState('') // 文件名称
  const [fileExt, setFileExt] = useState('') // 文件后缀名
  const [activeItemIndex, setActiveItemIndex] = useState(0) // 当前选中的item index

  const [fileNameid, setFileNameid] = useState('') // 文件名称

  const [uploadedList, setUploadedList] = useState<FormatUploadedFile[]>([]) // 已经上传的文件队列
  const [substandardList, setSubstandardList] = useState<FormatUploadedFile[]>([]) // 不符合标准文件列表
  const [previewFile, setPreviewFile] = useState({ url: '' }) // 预览文件
  let redirectUploadUrl = useSelector((state) => state.loginResult.loginResult.redirectUploadUrl) // 跳转到微信文件上传的地址
  redirectUploadUrl = decodeURIComponent(redirectUploadUrl)
  let pageNo = useRef(1)
  let pageSize = useRef(PAGE_SIZE)// 分页
  const total = useRef(0)  // 总条数
  // const [loadStatus,setLoadStatus]=useState(0) // 数据加载状态
  const loadStatus = useRef(0) // 数据加载状态
  const refresh = useRef(false) // 是否正在刷新
  const swipeCellRefs = useRef([])
  //函数
  // push 文件到文件队列里面
  function onPushFile(obj: FormatUploadedFile) {
    if (uploadedList.length === 0) {
      _setUploadedList(obj)
    } else {
      const index = uploadedList.findIndex((item) => item.name === obj.name)
      if (index >= 0) {
        obj._status = 'NAME_REPEAT'
        _setSubstandardList(obj)
      } else {
        _setUploadedList(obj)
      }
    }
  }
  // 设置已经上传的文件列表
  function _setUploadedList(obj: FormatUploadedFile) {
    setUploadedList((prevList) => {
      obj._status = 'UPLOADING'
      return [...prevList, obj]
    })
    _upload(obj)
  }
  // 设置不符合标准的文件列表
  function _setSubstandardList(obj: FormatUploadedFile) {
    setSubstandardList((prevList) => {
      const arr = [...prevList, obj]
      if (arr.length > 0) {
        onToggleSubstandardTc(true)
      }
      return arr
    })
  }

  // 上传弹窗开关
  function onToggleUploadTc(visible: boolean = false) {
    setUploadTypesPopup(visible)
  }

  // 格式不符合弹窗开关
  function onToggleSubstandardTc(visible: boolean = false) {
    setSubstandardPopup(visible)
    if (visible === false) {
      setSubstandardList([]);
    }
  }
  // input上传文件校验类型和大小,比较是否存在一次性上传两份同名文件
  function onValidateFiles(FileList: File[] = []) {
    const maxFiles = 10 // 一次最多上传10份
    const fileLength = FileList.length
    if (fileLength > maxFiles) {
      Taro.showToast({
        title: `已达选择文件上限(一次最多上传${maxFiles}份),您选择了${fileLength}份,请重新选择`,
        icon: 'none'
      })
      return false
    }
    /*
       先比对是否有同名文件，有则提示；同名的一份都不上传，不同名的往下走
       */
    const countMap = {};
    FileList.forEach((item) => {
      const { name } = item;
      countMap[name] = (countMap[name] || 0) + 1;
    });
    // 分割数组
    const sameArr = []; // 存放重复的 name 对象
    const leftArr = []; // 存放唯一的 name 对象
    FileList.forEach((item) => {
      if (countMap[item.name] > 1) {
        sameArr.push(item);
      } else {
        leftArr.push(item);
      }
    });
    if (sameArr.length > 0) {
      const _arr = sameArr.map((file) => {
        return {
          file,
          type: file.type,
          size: file.size,
          name: file.name,
          _status: 'NAME_REPEAT'
        };
      });
      setSubstandardList(_arr);
      onToggleSubstandardTc(true);
    }
    if (leftArr.length == 0) {
      return false;
    }

    const types = [...IMAGE_TYPES, ...OTHER_TYPES] //允许类型
    const extensions = [...IMAGE_EXTENSIONS, ...OTHER_EXTENSIONS] //允许扩展名
    for (let i = 0; i < leftArr.length; i++) {
      const file = leftArr[i] // 获取单张上传的文件
      const type = file.type // 文件类型
      const size = file.size // 文件大小
      const extension = getFileExt(file.name) // 文件扩展名
      const isImage = IMAGE_EXTENSIONS.includes(extension) // 是否是图片
      const maxSize = isImage ? MAXSIZE_IMAGE : MAXSIZE_OTHER // 允许的最大上传尺寸
      if (!file) {
        continue
      }
      const formatFileObj = {
        file: file,
        type: file.type,
        size: file.size,
        name: file.name,
        _status: "WAIT",  // WAIT:等待上传 UPLOADING:上传中  SIZE_EXCEEDS:大小超出限制 NOT_ALLOWED_FORMAT: 文件格式不支持  NAME_REPEAT :文件名重复
      }
      // 验证扩展名以及文件类型(防止用户通过修改扩展名进行欺骗上传)
      if (extensions.includes(extension) && types.includes(type)) {
        // 验证大小
        if (size > maxSize) {
          formatFileObj._status = 'SIZE_EXCEEDS'
          _setSubstandardList(formatFileObj)
          continue
        } else {
          onPushFile(formatFileObj)
        }
      } else {
        formatFileObj._status = 'NOT_ALLOWED_FORMAT'
        _setSubstandardList(formatFileObj)
        continue
      }
    }
    onToggleUploadTc(false)
    //移除input(type='file')节点
    document.getElementById('fileInput')?.remove()
  }

  // 选择文件上传方式
  async function onSelectUploadType(type: string) {
    const system = await Taro.getSystemInfoAsync()
    switch (type) {
      case 'weixin':
        setUploadTypesPopup(false)

        wx.miniProgram.navigateTo({
          url: redirectUploadUrl
        })
        // if (window.__wxjs_environment === 'miniprogram') {

        // } else {
        //   Taro.showToast({
        //     title: '请使用微信小程序进行上传',
        //     icon: 'none'
        //   })
        // }
        break
      case 'file':
        const input = document.createElement('input')
        input.id = 'fileInput'
        input.type = 'file'
        input.multiple = system.platform.toLocaleLowerCase() == "ios" ? true : false
        input.name = 'myFiles[]'
        input.addEventListener('change', onFileInputChange)
        document.body.appendChild(input)
        input.click()
        break
        break
    }
  }
  function onFileInputChange(e) {
    e.stopPropagation() // 阻止冒泡
    let files = e.target.files
    files = files instanceof Array ? files : [files[0]]
    onValidateFiles(files)
  }
  // 删除询问
  function onIsConfirmDelete(index) {
    Dialog_.confirm({
      title: '确认删除吗?',
      message: '',
      confirmButtonColor: "#1989FA",
    }).then((res) => {
      if (res == 'confirm') {
        onDelete(uploadedList[index])
      }
    })
  }
  // 确认删除
  async function onDelete(item) {
    Taro.showLoading({ title: '删除中...', mask: true })
    const res = await global.$R.request(`${apiMap.knowledge.delItem}/${item.id}`, {}, { method: "delete" })
    Taro.hideLoading()
    if (res?.code === global.CORRECT_CODE) {
      Taro.showToast({ title: '删除成功', mask: true, icon: 'success', duration: 1000 })
      setTimeout(() => {
        refreshPage()
      }, 800)
    } else {
      Taro.showToast({ title: res?.message, icon: 'none', duration: 3000 })
    }
  }

  // 接口：重试上传
  async function onRetry(event, item) {
    event.stopPropagation()
    Taro.showLoading({ title: '重新上传中...', mask: true })
    const res = await global.$R.post(`${apiMap.knowledge.retryItem}/${item.id}`)
    Taro.hideLoading()
    if (res?.code === global.CORRECT_CODE) {
      Taro.showToast({ title: '重新上传成功', icon: 'success', duration: 3000 })
      refreshPage()
    } else {
      Taro.showToast({ title: res?.message, icon: 'none', duration: 3000 })
    }
  }
  // 重新上传
  function onReupload() {
    setSubstandardList([])
    onToggleSubstandardTc(false)
    onToggleUploadTc(true)
  }
  // 打开详情
  function onOpenDetail(item) {
    const isImage = IMAGE_EXTENSIONS.includes(getFileExt(item.name)) // 是否是图片
    if (isImage) {
      setPreviewFile({ url: item.url })
      setPreviewTc(true)
    } else {
      wx.miniProgram.navigateTo({
        url: `/pages3/pages/previewFile/previewFile?url=${item.url}&name=${item.name}`
      })
    }
  }
  //接口：获取列表
  async function _page(callback = () => { }) {
    Taro.showLoading({ title: '加载中...', mask: true })
    const reqdata = {
      pageNo: pageNo.current,
      pageSize: pageSize.current,
      isAsc: "true",
      orderByColumn: ''
    }
    const res = await global.$R.get(apiMap.knowledge.page, reqdata)
    Taro.hideLoading()
    console.log("res", res)
    if (res?.code === global.CORRECT_CODE) {
      const resdata = res.data
      total.current = resdata.total
      if (typeof callback === 'function') {
        callback(resdata)
      }
      const status = getStatus({ total: total.current, page: pageNo.current, pageSize: pageSize.current })
      // setLoadStatus(status)
      loadStatus.current = status
    }
    return res
  }
  // 刷新列表
  function refreshPage(stopRefresh = false) {
    if (refresh.current) {
      return
    }
    _initData()
    refresh.current = true
    _page((data) => {
      refresh.current = false
      if (stopRefresh) {
        Taro.stopPullDownRefresh()
      }
      setUploadedList(data.records)
    })
  }
  //接口：上传
  async function _upload(obj: FormatUploadedFile) {
    Taro.showLoading({ title: '上传中', mask: true })
    const { file } = obj
    const formData = new FormData()
    formData.append('file', file)
    const res = await global.$R.post(apiMap.knowledge.upload, formData, { isFormData: true })
    Taro.hideLoading()
    if (res?.code === global.CORRECT_CODE) {
      refreshPage()
    } else {
      Taro.showToast({
        icon: "none",
        title: res?.message,
        duration: 3000
      })
    }
  }
  //初始化数据
  function _initData() {
    pageNo.current = 1
    pageSize.current = PAGE_SIZE
    // setLoadStatus(0)
    loadStatus.current = 0
    // setRefresh(false)
    refresh.current = false
  }
  // 上拉加载
  function onScrollToLower() {
    if (loadStatus.current === 0 || loadStatus.current === 2 || loadStatus.current === 3) {
      return false
    }
    loadStatus.current = 2
    pageNo.current++
    _page((data) => {
      setUploadedList((prevList) => {
        return [...prevList, ...data.records]
      })
    })
  }

  function onTogglePopover() {
    Dialog_.alert({
      title: '上传限制',
      zIndex: 10000,
      message: ' 支持PDF、Word文档（docx、doc）、PPT文件（PPT、PPTX）、TXT、JPG、JPEG、PNG、HEIC格式。图片文件单个大小不超过10MB，其他文件单个大小不超过50MB。',
      confirmButtonColor: "#1989FA",
    }).then((res) => {
    })
  }
  // 编辑
  function onEdit(event, index, items) {
    // 主动关闭当前SwipeCell
    swipeCellRefs.current[index]?.close?.();
    setFileNameid(items?.id)
    event.stopPropagation()
    onToggleEditTc(true)
    setActiveItemIndex(index)
    const item = uploadedList[index]
    // 分离主文件名和后缀名
    const name = item.name || ''
    const lastDot = name.lastIndexOf('.')
    if (lastDot > 0) {
      setFileName(name.substring(0, lastDot))
      setFileExt(name.substring(lastDot))
    } else {
      setFileName(name)
      setFileExt('')
    }
  }
  // 打开编辑弹层
  function onToggleEditTc(visible) {
    setShowEditTc(visible)
  }
  // 提交编辑修改
  const  onSubmitEdit=async()=> {
    // 拼接主文件名和后缀名
    const newName = fileName + fileExt
    const res = await  global.$R.post(apiMap.knowledge.fileEide, { id: fileNameid, name: newName })
    if (res?.code  ) {
      setShowEditTc(false)
      refreshPage()
    } else {
      Taro.showToast({
        icon: "none",
        title: res?.message,
        duration: 3000
      })
    }
  }

  // 钩子函数
  useEffect(() => {

  }, [])
  useDidShow(() => {
    refreshPage()
  })
  useReachBottom(() => {
    onScrollToLower()
  })
  usePullDownRefresh(() => {
    refreshPage(true)
  })

  function isWeappPC() {
    if (typeof window !== 'undefined' && window.__wxjs_environment === 'miniprogram') {
      const ua = navigator.userAgent.toLowerCase();
      return ua.includes('windowswechat') || ua.includes('macwechat') || ua.includes('wxwork');
    }
    return false;
  }

  // 页面节点
  return (
    <>
      <Dialog_ />
      <View className={`${styles.page}  flex flex-column`}>
        {uploadedList.length == 0 ? (
          // <View className="flex flex-column mheight-100v box-border">
          <View
            className={`text-center pb-5 flex-1 flex flex-column justify-center ${styles.emptyWrap}`}
          >
            <View>
              <Image
                className={styles['empty-pic']}
                mode="aspectFit"
                src={require('@/assets/images/<EMAIL>')}
              />
            </View>
            <View className={`font-weight-bold font mb-1 ${styles['empty-title']}`}>
              暂无知识库
            </View>
            <View className="font-xs text-second">
              <View>上传资料到知识库后，E人会优先从知识库检索信息。</View>
              <View>上传独有心得可以让AI拥有更深层的智慧</View>
            </View>
          </View>
          // </View>
        ) : (
          <ScrollView
            className="pt-base scrollViewWrap"
            style={{ flex: 1, height: '100vh', paddingBottom: '200px' }}
            scrollY
            onScrollToLower={onScrollToLower}
          >
            {(uploadedList instanceof Array) && uploadedList.map((item, index) => {
              const isPC = isWeappPC();
              const CellComponent = isPC ? SwipeCell : SwipeCell;
              return (
                <CellComponent
                  ref={el => swipeCellRefs.current[index] = el}
                  key={index}
                  rightWidth={110}
                  onClose={() => { }}
                  className="position-relative"
                  renderRight={
                    <View
                      className={`flex align-center  height-100 pr-base position-relative`}
                    >
                      <View
                        style={{backgroundColor: '#4F66FF',marginTop:`${isPC?'0px':'0px'}`}}
                        className={`flex align-center justify-center   mr-1  rounded-circle ${styles['item-del-icon']}`}
                        hoverClass="bg-black"
                        onClick={(event) => onEdit(event, index, item)}
                      >
                        <Image className={styles.delIcon} src={IOCN_EDIT}></Image>
                      </View>
                      <View
                        className={`flex align-center justify-center bg-danger  rounded-circle ${styles['item-del-icon']}`}
                        hoverClass="bg-black"
                        style={{ marginTop:`${isPC?'0px':'0px'}`}}
                        onClick={() => onIsConfirmDelete(index)}
                      >
                        <Image className={styles.delIcon} src={require('@/assets/images/delicon.png')}></Image>
                      </View>
                    </View>
                  }
                >
                  <View
                    className={`px-base mb-base`}
                  >
                    <ListItem
                      item={item}
                      index={index}
                      className={`bg-white ${styles['list-item-main']} `}
                      iconbgClassName="bg-light-muted"
                      onClick={() => onOpenDetail(item)}
                      renderRight={
                        (item.status == 3) &&
                        <View
                          className="ml-base flex-shrink text-primary bg-tag-primary item-tag py-1  px-3 rounded-xxm font-xs"
                          onClick={(event) => onRetry(event, item)}
                        >
                          重试
                        </View>
                      }
                    ></ListItem>
                  </View>
                </CellComponent>
              )
            })}
            <Loadmore status={loadStatus.current}></Loadmore>
          </ScrollView>
        )}
        {/*      */}
        {!showEditTc&&  <View className={`p-base flex justify-center  flex-shrink  position-fixed bottom-0 right-0 left-0  zindex-999  ${styles.fixFootBox}`}>
          <Button
            className="btn-theme-brand-default"
            round={false}
            onClick={() => onToggleUploadTc(true)}
          >
            上传资料
          </Button>
        </View>}
      
      </View>
      {/* 弹窗 */}
      {/* 上传类型选择 */}
      <Popup
        show={uploadTypesPopup}
        position="bottom"
        round
        style={{
          overflow: 'visible' // 关键：允许内容溢出
        }}
        onClose={() => onToggleUploadTc(false)}
      >
        <View className="position-relative px-base pb-4 box-border">
          <View className="flex width-100 align-center my-5">
            <View className="flex-1 text-center">
              <View className="font-weight-bold font flex align-center justify-center ">
                请选择上传方式
                <View className="p-1 " onClick={onTogglePopover}>
                  <Icon name="question-o" className="mt" size="18px" />
                </View>
              </View>
            </View>
            <View className="flex-shrink">
              <Icon
                name="cross"
                className="text-second"
                size="22px"
                onClick={() => onToggleUploadTc(false)}
              />
            </View>
          </View>
          <View className={`mb-5 width-100 ${styles['type-list']}`}>
            {(uploadTypes instanceof Array) && uploadTypes.map((item, index) => {
              return (
                <View
                  key={index}
                  className="p-base mb-base bg-light flex align-center position-relative rounded width-100"
                  onClick={() => {
                    onSelectUploadType(item.value)
                  }}
                >
                  <Image
                    className={` flex-shrink mr-base m-0 ${styles['type-pic']}`}
                    mode="aspectFit"
                    src={item.iconurl}
                  />
                  <View className="flex-1">{item.title}</View>
                  <View className="flex-shrink flex align-center">
                    {item.value === 'weixin' && (
                      <View className="mr-base type-hint font-xs bg-tag-primary rounded text-primary p-1 px-2">
                        推荐
                      </View>
                    )}
                    <Icon name="arrow" className="text-fourth" size="18px" />
                  </View>
                </View>
              )
            })}
          </View>
        </View>
      </Popup>
      {/* 不符合标准文件提醒 */}
      <Popup
        show={substandardPopup}
        round
        position="bottom"
        style={{
          overflow: 'visible'
        }}
        onClose={() => onToggleSubstandardTc(false)}
      >
        <View className="p-base pb-10">
          <View className="flex width-100">
            <View className="flex-1 text-center mb-base pl-4">
              <View className="font-weight-bold font-m mb flex align-center justify-center">
                文件上传错误
                <View className="p-1 " onClick={onTogglePopover}>
                  <Icon name="question-o" className="mt" size="18px" />
                </View>
              </View>
              <View className="font-xs text-fourth">以下文件存在问题，请重新上传</View>
            </View>
            <View className="flex-shrink">
              <Icon name="cross" size="24px" onClick={() => onToggleSubstandardTc(false)} />
            </View>
          </View>
          <View className={styles.repeatListbody}>
            {substandardList.map((item, index) => {
              return (
                <ListItem
                  item={item}
                  index={index}
                  key={index}
                  className="bg-light mb-base"
                ></ListItem>
              )
            })}
          </View>
          <View className="flex justify-center">
            <Button className="btn-theme-brand-default" onClick={onReupload}>
              重新上传
            </Button>
          </View>
        </View>
      </Popup>
      {/* 图片预览 */}
      <Popup show={previewTc} closeable={true} className={styles.imgPopup} style={{ '--popup-close-icon-color': '#000' } as React.CSSProperties} onClose={() => setPreviewTc(false)}>
        <View className='width-100vw height-100vh overflow-hidden   flex  align-center justify-center' style={{ backgroundColor: "transparent" }} onClick={() => setPreviewTc(false)}>
          <Image className={styles.previewImg} src={previewFile.url} /
          >
        </View>
      </Popup>
      {/* 编辑 */}
      <MyModal visible={showEditTc} title='编辑名称' cancel={() => onToggleEditTc(false)} submit={onSubmitEdit} close={() => onToggleEditTc(false)}>
        <View className='mb-1 width-100'>
          <Input type='text' placeholder='请输入文件名称' focus value={fileName} maxlength={50} className={styles.input} onInput={e => { setFileName(e.detail.value) }} placeholderClass={styles.placeholder}></Input>
          {/* {fileExt && <span style={{marginLeft: 4, color: '#888'}}>{fileExt}</span>} */}
        </View>
        {fileName.length >= 50 && <View className='text-danger font-xs'>最多50个字符</View>}
      </MyModal>
    </>
  )
}

export default Index
