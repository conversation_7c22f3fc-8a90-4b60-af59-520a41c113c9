const CORRECT_CODE = 200 //正确码
const HOST = process.env.TARO_APP_HOST // 域名
const BASE_URL = (process.env.TARO_APP_MODE).indexOf('_dev')>=0 ? process.env.TARO_APP_BASE_API : HOST //api请求的域名
console.log("config",process.env.NODE_ENV,process.env.TARO_APP_MODE,BASE_URL,process.env.TARO_APP_HOST )

export { HOST, BASE_URL, CORRECT_CODE }


// const CORRECT_CODE = 200 //正确码
// const HOST = process.env.TARO_APP_HOST // 域名
// const BASE_URL = process.env.NODE_ENV !== 'production' ? process.env.TARO_APP_BASE_API : process.env.TARO_APP_BASE_API //api请求的域名

// export { HOST, BASE_URL, CORRECT_CODE }
