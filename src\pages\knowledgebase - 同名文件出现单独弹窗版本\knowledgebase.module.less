// @import '@/assets/styles/var.less';
.page {
  background: linear-gradient(180deg, #dee6fd 20px, #fff 40%);
}
.item-del-icon {
  width: 80px;
  height: 80px;
}
.item-del-icon-confirm {
  height: 80px;
  // padding: 0 20px;
  border-radius: 40px;
  width: 220px;
  text-align: center;
  margin: 0 16px;
}

.repeatListbody {
  max-height: 60vh;
  overflow: auto;
}
.empty-pic {
  width: 532px;
}
.empty-title {
  margin-top: -20%;
}
.emptyWrap {
  padding-bottom: 150px;
}

//类型列表
.type-list {
  // width:88px;
  .type-pic {
    width: 58px;
  }
}
// 文件列表
.list-wrap {
}
.list-item-main {
  box-shadow: 0px 8px 20px 0px rgba(#c2cdf0, 20%);
}
