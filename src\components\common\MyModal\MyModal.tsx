import { View, Button } from '@tarojs/components'
import styles from './MyModal.module.less'
export default function Index(props) {
  const { visible, title, children, cancel, submit, close } = props
  function onCancel() {
     cancel()
  }
  function onSubmit() {
     submit()
  }
  function onClose() {
    close()
  }

  return (
    <>
      {visible && <View className={styles.tcWrap}>
        <View className={styles.tcMain}>
          <View className={styles.tcTitleWrap}>
            <View className={styles.tcTitle}>{title}</View>
            {/* <View className={styles.tcClose} onClick={onClose}>x</View> */}
          </View>
          <View className={styles.tcContentWrap}>
            {children}
          </View>
          <View className={styles.btnWrap}>
            <View className={styles.btnBox}>
              <Button className={styles.btnCancel} onClick={onCancel}>取消</Button>
            </View>
            <View className={styles.btnBox}>
              <Button className={styles.btnSubmit} onClick={onSubmit}>确定</Button>
            </View>
          </View>
        </View>
      </View>}
    </>
  )
}
