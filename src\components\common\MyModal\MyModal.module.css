.tcWrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 655;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  padding: 32px;
  width: 100%;
  height: 100vh;
  border: 10px solid;
}
.tcMain {
  background-color: #fff;
  border-radius: 24px;
  padding: 32px;
  width: 100%;
}
.tcTitleWrap {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  position: relative;
  padding: 0 50px;
}
.tcTitle {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #333;
  font-weight: bold;
}
.tcClose {
  position: absolute;
  right: 0;
  width: 56px;
  height: 56px;
  border-radius: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 0;
}
.tcContentWrap {
  width: 100%;
  margin-bottom: 32px;
}
.btnWrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.btnBox {
  width: 49%;
}
.btnCancel {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 88px;
  height: 88px;
  background-color: #eee;
  color: #777;
}
.btnCancel:after {
  content: "";
  display: none;
}
.btnSubmit {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88px;
  border-radius: 88px;
  background: #1989FA;
  color: #fff;
}
.btnSubmit:after {
  content: "";
  display: none;
}
