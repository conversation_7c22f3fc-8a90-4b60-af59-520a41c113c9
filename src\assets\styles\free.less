  /* <!--flex-1 要设置 height:0 ，不然子元素 无法 实现滚动--> */

/*颜色*/
@import "./var.less" ;
/* 防止图片闪一下 */
img{will-change:transform;}
*{box-sizing: border-box;margin: 0;padding: 0;}
body{
	--padbase:30px;
	--marbase:30px;
}
/* scroll-view 高宽 */
.scroll-row{width:100%;white-space: nowrap;overflow-x: auto;}
.scroll-row-item{display: inline-block;}
/* border-box */
.box-border{box-sizing: border-box;}

.row{
  display: flex;
	flex-wrap:wrap;
	flex-direction:row;
}
/*对其方式*/
.text-justify{
text-align-last:justify;
text-align:justify;
text-justify:distribute-all-lines;
}
/* 文字颜色 */
.text-red{color:@red;}
.text-black{color:@black;}
.text-white{color:@white;}

.text-themeBrand {color:@theme-brand;}
.text-themeBrand-muted {color:@theme-brand-muted;}

.text-primary{color:@primary;}
.text-primary-muted{color:@primary-muted;}
.text-primary-light{color:@primary-light;}
.text-success{color:@success;}
.text-success-muted{color:@success-muted;}
.text-success-light{color:@success-light;}
.text-danger{color:@danger ;}
.text-danger-muted{color:@danger-muted ;}
.text-danger-light{color:@danger-light ;}
.text-warning{color:@warning;}
.text-warning-muted{color:@warning-muted;}
.text-warning-light{color:@warning-light;}
.text-info{color:@info;}
.text-light{color:@light;}
.text-light-muted{color:@light-muted;}
.text-disabled{color:@disabled;}
.text-colorInput{color:@colorInput;}
.text-first{color:@first;}
.text-second{color:@second;}
.text-third{color:@third;}
.text-fourth{color:@fourth;}
//特殊
.text-icon-primary{color:@icon-primary;}
.text-doc-theme-brand{color:@doc-theme-brand;}
.text-status-primary{color:@status-primary;}
.text-status-warning{color:@status-warning;}
.text-status-fail{color:@status-fail;}
.text-status-hint{color:@status-hint;}

/* 背景颜色 */
 .bg-red{background-color:@red;}
 .bg-black{background-color:@black;}
 .bg-white{background-color:@white;}

 .bg-themeBrand {background-color:@theme-brand;}
 .bg-themeBrand-muted {background-color:@theme-brand-muted;}
 .bg-primary{background-color:@primary;}
 .bg-primary-muted{background-color:@primary-muted;}
 .bg-primary-light{background-color:@primary-light;}
 .bg-success{background-color:@success;}
 .bg-success-muted{background-color:@success-muted;}
 .bg-success-light{background-color:@success-light;}
 .bg-danger{background-color:@danger ;}
 .bg-danger-muted{background-color:@danger-muted ;}
 .bg-danger-light{background-color:@danger-light ;}
 .bg-warning{background-color:@warning;}
 .bg-warning-muted{background-color:@warning-muted;}
 .bg-warning-light{background-color:@warning-light;}
 .bg-info{background-color:@info;}
 .bg-light{background-color:@light;}
 .bg-light-muted{background-color:@light-muted;}
 .bg-disabled{background-color:@disabled;}
 .bg-input{background-color:@colorInput;}
 //特殊
 .bg-avatar{background-color:@avatar;}
 .bg-icon-primary{background-color:@icon-primary;}
 .bg-doc-theme-brand{background-color:@doc-theme-brand;}
 .bg-doc-theme-brand-im{background-color:@doc-theme-brand!important;}
 .bg-status-primary{background-color:@status-primary;}
.bg-status-warning{background-color:@status-warning;}
.bg-status-fail{background-color:@status-fail;}
.bg-status-hint{background-color:@status-hint;}
.bg-tag-primary{background-color:@tag-primary;}

/* 行高 */
.lineh-1{line-height:1}
.lineh-1-2{line-height:1.2}
.lineh-1-4{line-height:1.4}
.lineh-1-6{line-height:1.6}
.lineh-2{line-height:2}

/* 宽度和高度 */
.width-100{width:100%;}
.maxwidth-100{max-width:100%;}
.width-100vw{width:100vw;}
.w-100{width:100%!important;}
.width-auto{width:auto;}
.height-auto{height:auto;}
.height-100{height:100%;}
.h-100{height:100%!important;}
.height-100vh{height:100vh;}
.mheight-100{min-height:100%}
.mheight-100vh{min-height:100vh}
.mheight-inherit{min-height: inherit;}
/*列*/
.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12{
	position:relative;
}
/* 栅栏一/  分成12分 每份是8.3%*/
.col-1{width:62.5px;}
.col-2{ width: 125px; }
.col-3{ width: 187.5px; }
.col-4{ width: 250px;}
.col-5{ width: 312.5px; }
.col-6{ width: 375px; }
.col-7{ width: 437.5px; }
.col-8{ width: 500px; }
.col-9{ width: 562.5px; }
.col-10{ width: 625px; }
.col-11{ width: 687.5px; }
.col-12{ width: 750px; }

// .col-12{width:100%;}
// .col-11 { width:91.65%; }
// .col-10 { width:83.32%; }
// .col-9 { width:74.99%; }
// .col-8 { width:66.66%; }
// .col-7 { width:58.33%; }
// .col-6 { width:50%; }
// .col-5 { width:41.65%;}
// .col-4 {width: 33.32%;}
// .col-3 {width:24.99%;}
// .col-2 {width: 16.66%;}
// .col-1 {width:8.33%;}

/* 栅栏二 分成20分 每份是5% */
.span-1{width:5%;}
.span-2{ width: 10%; }
.span-3{ width: 15%; }
.span-4{ width: 20%;}
.span-5{ width: 25%; }
.span-6{ width: 30%; }
.span-7{ width: 35%; }
.span-8{ width: 40%; }
.span-9{ width: 45%; }
.span-10{ width: 50%; }
.span-11{ width: 55%; }
.span-12{ width: 60%; }
.span-13{ width: 65%; }
.span-14{ width: 70%; }
.span-15{ width: 75%; }
.span-16{ width: 80%; }
.span-17{ width: 85%; }
.span-18{ width: 90%; }
.span-19{ width: 95%; }
.span-20{ width: 100%; }

/* 栅栏三 */
.span24-1{width:4.17%;}
.span24-2{ width: 8.33%; }
.span24-3{ width: 12.5%; }
.span24-4{ width: 16.67%;}
.span24-5{ width: 20.83%; }
.span24-6{ width: 25%; }
.span24-7{ width: 29.17%; }
.span24-8{ width: 33.33%; }
.span24-9{ width: 37.5%; }
.span24-10{ width: 41.67%; }
.span24-11{ width: 45.83%; }
.span24-12{ width: 50%; }
.span24-13{ width: 54.17%; }
.span24-14{ width: 58.33%; }
.span24-15{ width: 62.5%; }
.span24-16{ width: 66.67%; }
.span24-17{ width: 70.83%; }
.span24-18{ width: 75%; }
.span24-19{ width: 79.17%; }
.span24-20{ width: 83.33%; }
.span24-21{ width: 87.5%; }
.span24-22{ width: 91.67%; }
.span24-23{ width: 95.83%; }
.span24-24{ width: 100%; }

/* flex 布局 */
.flex{display: flex;flex-direction: row;}
.flex-row{flex-direction:row}
.flex-column{flex-direction:column}
.flex-row-reverse{flex-direction: row-reverse;}
.flex-column-reverse{ flex-direction:column-reverse; }
.flex-wrap{flex-wrap:wrap;}
.flex-nowrap{flex-wrap:nowrap;}

.justify-start{justify-content: flex-start;}
.justify-end{justify-content: flex-end;}
.justify-between{justify-content: space-between;}
.justify-center{justify-content: center;}
.justify-around{justify-content:space-around;}
.justify-evenly{justify-content: space-evenly;}

.align-center{align-items:center;}
.align-stretch{align-items: stretch;}
.align-start{align-items: flex-start;}
.align-end{ align-items: flex-end; }
.align-self-end{ align-self: flex-end; }
.align-self-center{ align-self: flex-center; }
.align-self-start{ align-self: flex-start; }

.content-start{align-content:flex-start;}
.content-end{align-content: flex-end;}
.content-center{align-content: center;}
.content-between{align-content: space-between;}
.content-around{align-content:space-around;}
.content-stretch{align-content: stretch;}
.content-evenly{align-content: space-evenly;}

.flex-1{flex:1;flex-grow:1;flex-shrink:1;flex-basis:0;}
.flex-2{flex:2;}
.flex-3{ flex: 3; }
.flex-4{ flex: 4; }
.flex-5{ flex: 5; }
.flex-shrink{flex-shrink: 0;}

/* Spacing */
.m-0 { margin-left: 0;margin-right: 0;margin-top: 0;margin-bottom: 0;}
.m-base { margin:var(--marbase);}
.m { margin-left: 5px;margin-right: 5px;margin-top: 5px;margin-bottom: 5px;}
.m-1 { margin-left: 10px;margin-right: 10px;margin-top: 10px;margin-bottom: 10px;}
.m-2 { margin-left: 20px;margin-right: 20px;margin-top: 20px;margin-bottom: 20px;}
.m-3 { margin-left: 30px;margin-right: 30px;margin-top: 30px;margin-bottom: 30px;}
.m-4 { margin-left: 40px;margin-right: 40px;margin-top: 40px;margin-bottom: 40px;}
.m-5 { margin-left: 50px;margin-right: 50px;margin-top: 50px;margin-bottom: 50px;}

.mx-0 { margin-left: 0;margin-right: 0;}
.mx-base{margin-left: var(--marbase);margin-right: var(--marbase);}
.mx { margin-left: 5px;margin-right: 5px;}
.mx-auto{ margin-left: auto;margin-right: auto;}
.mx-1 { margin-left: 10px;margin-right: 10px;}
.mx-2 { margin-left: 20px;margin-right: 20px;}
.mx-3 { margin-left: 30px;margin-right: 30px;}
.mx-4 { margin-left: 40px;margin-right: 40px;}
.mx-5 { margin-left: 50px;margin-right: 50px;}

.my-0 { margin-top: 0;margin-bottom: 0;}
.my-base { margin-top: var(--marbase);margin-bottom: var(--marbase);}
.my { margin-top: 5px;margin-bottom: 5px;}
.my-1 { margin-top: 10px;margin-bottom: 10px;}
.my-2 { margin-top: 20px;margin-bottom: 20px;}
.my-3 { margin-top: 30px;margin-bottom: 30px;}
.my-4 { margin-top: 40px;margin-bottom: 40px;}
.my-5 { margin-top: 50px;margin-bottom: 50px;}
.my-6 { margin-top: 60px;margin-bottom: 60px;}
.my-7 { margin-top: 70px;margin-bottom: 70px;}
.my-8 { margin-top: 80px;margin-bottom: 80px;}
.my-9 { margin-top: 90px;margin-bottom: 90px;}
.my-10 { margin-top: 100px;margin-bottom: 100px;}

.mt-0 { margin-top: 0;}
.mt-base { margin-top: var(--marbase);}
.mt { margin-top: 5px;}
.mt-auto { margin-top: auto;}
.mt-1 { margin-top: 10px;}
.mt-2 { margin-top: 20px;}
.mt-3 { margin-top: 30px;}
.mt-4 { margin-top: 40px;}
.mt-5 { margin-top: 50px;}
.mt-6 { margin-top: 60px;}
.mt-7 { margin-top: 70px;}
.mt-8 { margin-top: 80px;}
.mt-9 { margin-top: 90px;}
.mt-10 { margin-top: 100px;}

.mb { margin-bottom: 5px;}
.mb-base{margin-bottom:var(--marbase);}
.mb-auto { margin-bottom: auto;}
.mb-1 { margin-bottom: 10px;}
.mb-2 { margin-bottom: 20px;}
.mb-3 { margin-bottom: 30px;}
.mb-4 { margin-bottom: 40px;}
.mb-5 { margin-bottom: 50px;}
.mb-6 { margin-bottom: 60px;}
.mb-7 { margin-bottom: 70px;}
.mb-8 { margin-bottom: 80px;}
.mb-9 { margin-bottom: 90px;}
.mb-10 { margin-bottom: 100px;}
.mb-0 { margin-bottom: 0;}

.ml-0 { margin-left: 0;}
.ml-base { margin-left: var(--marbase);}
.ml { margin-left: 5px;}
.ml-auto { margin-left: auto;}
.ml-1 { margin-left: 10px;}
.ml-2 { margin-left: 20px;}
.ml-3 { margin-left: 30px;}
.ml-4 { margin-left: 40px;}
.ml-5 { margin-left: 50px;}
.ml-100-{margin-left:-100%}

.mr-0 { margin-right: 0;}
.mr-base { margin-right: var(--marbase);}
.mr { margin-right: 5px;}
.mr-1 { margin-right: 10px;}
.mr-2 { margin-right: 20px;}
.mr-3 { margin-right: 30px;}
.mr-4 { margin-right: 40px;}
.mr-5 { margin-right: 50px;}

.p-0 {padding-left: 0;padding-right: 0;padding-top: 0;padding-bottom: 0;}
.p-base{padding:var(--padbase);}
.p {padding-left: 5px;padding-right: 5px;padding-top: 5px;padding-bottom:5px;}
.p-1 {padding-left: 10px;padding-right: 10px;padding-top: 10px;padding-bottom: 10px;}
.p-2 {padding-left: 20px;padding-right: 20px;padding-top: 20px;padding-bottom: 20px;}
.p-3 {padding-left: 30px;padding-right: 30px;padding-top: 30px;padding-bottom: 30px;}
.p-4 {padding-left: 40px;padding-right: 40px;padding-top: 40px;padding-bottom: 40px;}
.p-5 {padding-left: 50px;padding-right: 50px;padding-top: 50px;padding-bottom: 50px;}

.px-0 { padding-left: 0;padding-right: 0;}
.px-base{padding-left:var(--padbase);padding-right:var(--padbase);}
.px { padding-left: 5px;padding-right: 5px;}
.px-1 { padding-left: 10px;padding-right: 10px;}
.px-2 { padding-left: 20px;padding-right: 20px;}
.px-3 { padding-left: 30px;padding-right: 30px;}
.px-4 { padding-left: 40px;padding-right: 40px;}
.px-5 { padding-left: 50px;padding-right: 50px;}

.py-0 { padding-top: 0;padding-bottom: 0;}
.py-base{padding-top:var(--padbase);padding-bottom:var(--padbase);}
.py { padding-top: 5px;padding-bottom: 5px;}
.py-1 { padding-top: 10px;padding-bottom: 10px;}
.py-2 { padding-top: 20px;padding-bottom: 20px;}
.py-3 { padding-top: 30px;padding-bottom: 30px;}
.py-4 { padding-top: 40px;padding-bottom: 40px;}
.py-5 { padding-top: 50px;padding-bottom: 50px;}

.pt-0 { padding-top: 0;}
.pt-base{padding-top:var(--padbase);}
.pt { padding-top: 5px;}
.pt-1 { padding-top: 10px;}
.pt-2 { padding-top: 20px;}
.pt-3 { padding-top: 30px;}
.pt-4 { padding-top: 40px;}
.pt-5 { padding-top: 50px;}
.pt-7 { padding-top: 70px;}
.pt-8 { padding-top: 80px;}
.pt-9 { padding-top: 90px;}
.pt-10 { padding-top: 100px;}

.pb-0 { padding-bottom: 0;}
.pb-base{padding-bottom:var(--padbase);}
.pb { padding-bottom: 5px;}
.pb-1 { padding-bottom: 10px;}
.pb-2 { padding-bottom: 20px;}
.pb-3 { padding-bottom: 30px;}
.pb-4 { padding-bottom: 40px;}
.pb-5 { padding-bottom: 50px;}
.pb-6 { padding-bottom: 60px;}
.pb-7 { padding-bottom: 70px;}
.pb-8 { padding-bottom: 80px;}
.pb-9 { padding-bottom: 90px;}
.pb-10 { padding-bottom: 100px;}

.pl-0 { padding-left: 0;}
.pl-base { padding-left: var(--padbase);}
.pl { padding-left: 5px;}
.pl-1 { padding-left: 10px;}
.pl-2 { padding-left: 20px;}
.pl-3 { padding-left: 30px;}
.pl-4 { padding-left: 40px;}
.pl-5 { padding-left: 50px;}

.pr-0 { padding-right: 0;}
.pr-base{padding-right:var(--padbase);}
.pr { padding-right: 5px;}
.pr-1 { padding-right: 10px;}
.pr-2 { padding-right: 20px;}
.pr-3 { padding-right: 30px;}
.pr-4 { padding-right: 40px;}
.pr-5 { padding-right: 50px;}

/* 外边距 */
// .m-0{margin:0}
// .m-base { margin:var(--marbase);}
// .m{margin:5px;}
// .m-1{margin:10px;}
// .m-2 { margin: 20px; }
// .m-3 { margin: 30px; }
// .m-4 { margin: 40px; }
// .m-5 { margin: 50px; }
// .m-auto{margin:auto;}

// .my-0{margin-top: 0;margin-bottom: 0;}
// .my-base{margin-top:var(--marbase);margin-bottom:var(--marbase);}
// .my{ margin-top: 5px;margin-bottom: 5px;}
// .my-1 { margin-top: 10px; margin-bottom: 10px; }
// .my-2 { margin-top: 20px; margin-bottom: 20px; }
// .my-3 { margin-top: 30px; margin-bottom: 30px; }
// .my-4 { margin-top: 40px; margin-bottom: 40px; }
// .my-5 { margin-top: 50px; margin-bottom: 50px; }
// .my-auto { margin-top: auto; margin-bottom: auto; }

// .mx-0 { margin-left: 0; margin-right: 0; }
// .mx-base { margin-left:var(--marbase); margin-right:var(--marbase); }
// .mx { margin-left: 5px;margin-right: 5px;}
// .mx-1 { margin-left: 10px; margin-right: 10px;}
// .mx-2 { margin-left: 20px; margin-right: 20px;}
// .mx-3 { margin-left: 30px; margin-right: 30px;}
// .mx-4 { margin-left: 40px; margin-right: 40px;}
// .mx-5 { margin-left: 50px; margin-right: 50px;}
// .mx-auto { margin-left: auto; margin-right: auto; }

// .ml-0{margin-left:0;}
// .ml{margin-left:5px;}
// .ml-base{margin-left:var(--marbase);}
// .ml-1 { margin-left: 10px; }
// .ml-2 { margin-left: 20px; }
// .ml-3 { margin-left: 30px; }
// .ml-4 { margin-left: 40px; }
// .ml-5 { margin-left: 50px; }
// .ml-auto { margin-left: auto; }

// .mt-0 { margin-top: 0; }
// .mt{margin-top:5px;}
// .mt-base{margin-top:var(--marbase);}
// .mt-1{margin-top:10px;}
// .mt-2 { margin-top: 20px; }
// .mt-3 { margin-top: 30px; }
// .mt-4 { margin-top: 40px; }
// .mt-5 { margin-top: 50px; }
// .mt-auto { margin-top: auto; }

// .mr-0 { margin-right: 0; }
// .mr{margin-right:5px;}
// .mr-base{margin-right:var(--marbase);}
// .mr-1 { margin-right: 10px; }
// .mr-2 { margin-right: 20px; }
// .mr-3 { margin-right: 30px; }
// .mr-4 { margin-right: 40px; }
// .mr-5 { margin-right: 50px; }
// .mr-auto { margin-right: auto; }

// .mb-0{margin-bottom:0}
// .mb{margin-bottom:5px;}
// .mb-base{margin-bottom:var(--marbase);}
// .mb-1{margin-bottom:10px;}
// .mb-2 { margin-bottom: 20px; }
// .mb-3 { margin-bottom: 30px; }
// .mb-4 { margin-bottom: 40px; }
// .mb-5 { margin-bottom: 50px; }
// .mb-auto { margin-bottom: auto; }

// /* 内边距 */

// .p-0{padding:0;}
// .p{padding:5px;}
// .p-base{padding:var(--padbase);}
// .p-1{padding:10px;}
// .p-2 { padding: 20px; }
// .p-3 { padding: 30px; }
// .p-4 { padding: 40px; }
// .p-5 { padding: 50px; }

// .py-0 { padding-top: 0; padding-bottom: 0; }
// .py{padding-top:5px;padding-bottom:5px;}
// .py-base{padding-top:var(--padbase);padding-bottom:var(--padbase);}
// .py-1 { padding-top: 10px; padding-bottom: 10px; }
// .py-2 { padding-top: 20px; padding-bottom: 20px; }
// .py-3 { padding-top: 30px; padding-bottom: 30px; }
// .py-4 { padding-top: 40px; padding-bottom: 40px; }
// .py-5 { padding-top: 50px; padding-bottom: 50px; }

// .px-0 { padding-left: 0; padding-right: 0; }
// .px{padding-left:5px;padding-right:5px;}
// .px-base{padding-left:var(--padbase);padding-right:var(--padbase);}
// .px-1 { padding-left: 10px; padding-right: 10px;}
// .px-2 { padding-left: 20px; padding-right: 20px;}
// .px-3 { padding-left: 30px; padding-right: 30px;}
// .px-4 { padding-left: 40px; padding-right: 40px;}
// .px-5 { padding-left: 50px; padding-right: 50px;}

// .pr-0 { padding-right: 0; }
// .pr{ padding-right: 5px; }
// .pr-base{padding-right:var(--padbase);}
// .pr-1 { padding-right: 10px; }
// .pr-2 { padding-right: 20px; }
// .pr-3 { padding-right: 30px; }
// .pr-4 { padding-right: 40px; }
// .pr-5 { padding-right: 50px; }

// .pt-0{padding-top: 0;}
// .pt{padding-top: 5px;}
// .pt-base{padding-top:var(--padbase);}
// .pt-1{padding-top:10px;}
// .pt-2 { padding-top: 20px; }
// .pt-3 { padding-top: 30px; }
// .pt-4 { padding-top: 40px; }
// .pt-5 { padding-top: 50px; }

// .pl-0 { padding-left: 0; }
// .pl{padding-left:5px;}
// .pl-base{padding-left:var(--padbase);}
// .pl-1 { padding-left: 10px; }
// .pl-2 { padding-left: 20px; }
// .pl-3 { padding-left: 30px; }
// .pl-4 { padding-left: 40px; }
// .pl-5 { padding-left: 50px; }

// .pb-0{padding-bottom: 0;}
// .pb{ padding-bottom: 5px; }
// .pb-base { padding-bottom:var(--padbase); }
// .pb-1{padding-bottom: 10px;}
// .pb-2 { padding-bottom: 20px; }
// .pb-3 { padding-bottom: 30px; }
// .pb-4 { padding-bottom: 40px; }
// .pb-5 { padding-bottom: 50px; }

/* 文字大小 */
// .font{font-size:16px;}
// .font-s{font-size:14px;}
// .font-xs{font-size:12px;}
// .font-m{font-size:18px;}
// .font-xm{font-size:20px;}
// .font-l{font-size:22px;}
// .font-xl{font-size:24px;}
// .font-xxl{font-size:26px;}
// .font-big{font-size:30px;}
// .font-xbig{font-size:32px;}

.font{font-size:32px;}
.font-s{font-size:30px;}
.font-xs{font-size:26px;}
.font-xxs{font-size:24px;}
.font-m{font-size:36px;}
.font-xm{font-size:40px;}
.font-l{font-size:46px;}
.font-xl{font-size:50px;}
.font-xxl{font-size:56px;}
.font-big{font-size:60px;}
.font-xbig{font-size:66px;}
.font-inherit{font-size: inherit;}

/* 文字粗细和斜体 */
.font-weight-normal{font-weight:normal;}
.font-weight-bold{font-weight:bold;}
.font-weight-100{font-weight:100;}
.font-weight-200{font-weight:200;}
.font-weight-500{font-weight:500;}
/*文字样式*/
.font-italic{font-style:italic} /*斜体*/

/* 文字缩进 */
.text-indent{text-indent: 2;}
.text-indent-4{text-indent:8px;}
.text-indent-6{text-indent:12px;}

/*文字间隔*/
.letter-spacing-2{letter-spacing: 4px;}
.letter-spacing-4{letter-spacing: 8px;}
.letter-spacing-6{letter-spacing: 12px;}
.letter-spacing-8{letter-spacing: 16px;}
/* 文字划线 */
.text-through{text-decoration: line-through;}
.text-underline{text-decoration:underline;}
.text-hover-underline:hover{text-decoration:underline;}

/* 文字对齐 */
.text-left{text-align:left}
.text-center{text-align:center;}
.text-right{text-align:right}
.text-justify{text-align:justify;text-align-last: justify;}
/* 垂直对齐 */
.vertical-middle{vertical-align:middle;}
.vertical-baseline{vertical-align:baseline;}
.vertical-top{vertical-align:text-top;}
.vertical-bottom{vertical-align:text-bottom;}

 /* 边框 */
 .border{border-width:1px;border-style:solid;border-color:@borderColor}
 .border-top{
	 border-top-width:1px;
	 border-top-style:solid;
 }
 .border-right{
	 border-right-width:1px;
	 border-right-style:solid;
 }
 .border-bottom{
	 border-bottom-width:1px;
	 border-bottom-style:solid;
 }
 .border-left{
	 border-left-width:1px;
	 border-left-style:solid;
}
 .border-light-bottom{
 	 border-bottom-width:1px;
 	 border-bottom-style:solid;
 }
 .border-bottom-dashed{
  border-bottom-width:1px;
 	 border-bottom-style:dashed;
}
 .border-0{border-width:0;}
 .border-top-0 { border-top-width: 0;}
 .border-right-0 {border-right-width: 0;}
 .border-bottom-0 {border-bottom-width: 0;}
 .border-left-0 {border-left-width: 0;}
 .border-top-transparent{border-top:1px solid transparent}
 .border-bottom-transparent{border-bottom:1px solid transparent}
 .border-right-transparent{border-right:1px solid transparent}
 .border-left-transparent{border-left:1px solid transparent}
 /* 边框颜色 */
 .border{border-color:@borderColor;}
 .border-top{border-top-color:@borderColor;}
 .border-right{
  border-right-color:@borderColor;
 }
 .border-bottom{
  border-bottom-color:@borderColor;
 }
 .border-left{
  border-left-color:@borderColor;
 }

 .border-red{border:1px solid;border-color:@red;}
 .border-black{border-color:@black;}
 .border-white{border-color:@white;}

 .border-themeBrand {border-color:@theme-brand;}
 .border-primary{border-color:@primary;}
 .border-primary-muted{border-color:@primary-muted;}
 .border-primary-light{border-color:@primary-light;}
 .border-success{border-color:@success;}
 .border-success-muted{border-color:@success-muted;}
 .border-success-light{border-color:@success-light;}
 .border-danger{border-color:@danger ;}
 .border-danger-muted{border-color:@danger-muted ;}
 .border-danger-light{border-color:@danger-light ;}
 .border-warning{border-color:@warning;}
 .border-warning-muted{border-color:@warning-muted;}
 .border-warning-light{border-color:@warning-light;}
 .border-info{border-color:@info;}
 .border-light{border-color:@light;}
 .border-light-muted{border-color:@light-muted;}
 .border-muted{border-color:@borderColor-muted;}
 .border-colorInput{border-color:@colorInput;}
 .border-dashed{border-style:dashed}
 /*圆角*/
 .rounded-s{border-radius: 8px;}
 .rounded{border-radius: 16px;}
 .rounded-m{border-radius: 20px;}
 .rounded-xm{border-radius: 30px;}
 .rounded-xxm{border-radius: 40px;}
 .rounded-circle{border-radius:100%;}
 .rounded-0{border-radius:0;}

 .rounded-sm-right{
 	 border-top-right-radius:4px;
 	 border-bottom-right-radius:4px;
 }
 .rounded-sm-top{
	 border-top-left-radius:4px;
	 border-top-right-radius:4px;
 }
 .rounded-sm-left{
	 border-top-left-radius:4px;
	 border-bottom-left-radius:4px;
 }
 .rounded-sm-bottom{
	 border-bottom-left-radius:4px;
	 border-bottom-right-radius:4px;
 }

 .rounded-md-right{
 	 border-top-right-radius:8px;
 	 border-bottom-right-radius:8px;
 }
 .rounded-md-top{
	 border-top-left-radius:8px;
	 border-top-right-radius:8px;
 }
 .rounded-md-left{
	 border-top-left-radius:8px;
	 border-bottom-left-radius:8px;
 }
 .rounded-md-bottom{
	 border-bottom-left-radius:8px;
	 border-bottom-right-radius:8px;
 }

 .rounded-right{
 	 border-top-right-radius:12px;
 	 border-bottom-right-radius:12px;
 }
 .rounded-top{
 	 border-top-left-radius:12px;
 	 border-top-right-radius:12px;
 }
 .rounded-left{
 	 border-top-left-radius:12px;
 	 border-bottom-left-radius:12px;
 }
 .rounded-bottom{
 	 border-bottom-left-radius:12px;
 	 border-bottom-right-radius:12px;
 }
 /* 显示 */
 .d-none{display: none;}
 .d-inline-block{display:inline-block;}
 .d-block{display: block;}
 .d-flex{display:flex;}
 .flex{display: flex;}
 /* 内容溢出 */
 .overflow-auto{overflow:auto;}
 .overflow-hidden{overflow: hidden;}
 .overflow-y-auto{overflow-y: auto;}
 .overflow-x-auto{overflow-x: auto;}
 .overflow-y-hidden{overflow-y: hidden;}
 .overflow-x-hidden{overflow-x: hidden;}
 /* 文字换行溢出处理
overflow: hidden;
display: -webkit-box;
-webkit-line-clamp: 3;
-webkit-box-orient: vertical;（多行文字
*/
.nowrap{white-space: nowrap;}
.text-ellipsis{
	overflow:hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
/* 多行文本隐藏 */
.text-ellipsis-2{
	overflow : hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
.text-overflow{overflow:hidden;white-space: nowrap;}
 /* 定位 */
 .position-relative{position:relative;}
 .position-absolute{position:absolute;}
 .position-fixed{position:fixed;}
 /* 定位 - 固定顶部 */
 .fixed-top{
	 position:fixed;
	 top:0;
	 right:0;
	 left:0;
	 z-index:555;
 }
 /* 定位 - 固定底部 */
.fixed-bottom{
	position:fixed;
	bottom:0;
	right:0;
	left:0;
	z-index:555;
}
.right-0{right:0;}
.top-0{top:0;}
.left-0{left:0;}
.bottom-0{bottom:0;}
/*鼠标样式*/
 .cursor-pointer{cursor: pointer;}
 .cursor-default{cursor: default;}
 .cursor-auto{cursor:auto}
 /* 多行文本隐藏 */
.text-ellipsis{
	overflow:hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.text-ellipsis-2{
	overflow : hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
.text-overflow{
	overflow:hidden;
	white-space: nowrap;
}
.word-break{
  word-wrap: break-word;
  word-break: break-all;
}
 /* 内容溢出 */
 .overflow-hidden{overflow: hidden;}
 .overflow-auto{overflow: auto;}
 .overflow-y-auto{overflow-y: auto;}
 .overflow-x-auto{overflow-x: auto;}
 .overflow-y-hidden{overflow-y: hidden;}
 .overflow-x-hidden{overflow-x: hidden;}
 .overflow-visible{overflow: visible;}
 /* 垂直对齐 */
 .vertical-middle{vertical-align:middle;}
 .vertical-baseline{vertical-align:baseline;}
 .vertical-top{vertical-align:text-top;}
 .vertical-bottom{vertical-align:text-bottom;}
 /*缩放*/
 .scale-primary{transform: scale(0.7);}
.scale-70{transform: scale(0.7);}
.scale-80{transform: scale(0.8);}
.scale-90{transform: scale(0.9);}
// .radio-hide{}

/*shadow 阴影*/
.shadow-xs{box-shadow: 0px 1px 0px 0px #E6E6E6;}
.shadow{box-shadow: 0px 4px 10px -2px rgba(0, 0, 0, 0.22)}
.shadow-sm{box-shadow: 0px 4px 8px -2px rgba(0, 0, 0, 0.12),0px 8px 10px 0px rgba(0, 0, 0, 0.08),0px 2px 20px 0px rgba(0, 0, 0, 0.05);}
.shadow-md{box-shadow: 0px 10px 10px -3px rgba(0, 0, 0, 0.1),0px 16px 20px 2px rgba(0, 0, 0, 0.06),0px 6px 28px 4px rgba(0, 0, 0, 0.05);}
.shadow-lg{box-shadow: 0px 16px 20px -10px rgba(0, 0, 0, 0.08),0px 32px 48px 4px rgba(0, 0, 0, 0.04),0px 12px 60px 10px rgba(0, 0, 0, 0.05);}

/* 浮动 */
.float-right{float:right}
.float-left{float:left}

/* 过渡 */
.transition-all-0_5{transition:all 0.5s}
.opacity-0_5{opacity: 0.5;}

/*层级*/
.zindex-1{z-index:1}
.zindex-2{z-index:2}
.zindex-3{z-index:3}
.zindex-4{z-index:4}
.zindex-5{z-index:5}
.zindex-999{z-index:999}
.zindex-1000{z-index:10001}

/*div:after*/
.div-after-absolute:after{
	content:"";
	z-index:-1;
	position:absolute;
	left:0;
	top:0;
	right:0;
	bottom:0;
}
.div-before-absolute:before{
	content:"";
	z-index:-1;
	position:absolute;
	left:0;
	top:0;
	right:0;
	bottom:0;
}

