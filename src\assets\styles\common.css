/* 主色调 */
/*蓝色*/
/*主题色*/
/*
重试 #4F66FF;
等待上传 #3D66E4
*/
/*绿色*/
/*绿色*/
/*绿色*/
/*红色*/
/*红色*/
/*红色*/
/*黄色*/
/*黄色*/
/*黄色*/
/*详情*/
/*填充：浅色/默认，跟白色无差的灰色,用于背景*/
/*填充深色*/
/*状态/进度颜色*/
/*红色*/
/*黑色*/
/*白色*/
/*字体颜色*/
/*字体高强调*/
/*灰色*/
/*灰色 用于副标题 备注这种*/
/*灰色 用于副标题 备注这种*/
/*边框颜色*/
/*边框浅灰色*/
/*边框深灰色*/
/*form*/
/*对应鼠标按下去的颜色*/
/*以下为兼容历史，尽量不用，只保留*/
/*字体色调*/
* {
  padding: 0;
  margin: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
html,
body {
  min-height: 100%;
  font-size: 100%;
  overflow: auto;
}
body {
  font-family: 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, '宋体';
  word-wrap: break-word;
  word-break: break-all;
}
header,
nav,
main,
footer,
section,
article,
aside,
figure,
figcaption {
  display: block;
}
h1,
h2,
h3,
h4,
h5,
h6,
i,
b,
em {
  font-weight: normal;
  font-style: normal;
}
ul,
ol {
  list-style-type: none;
}
ins {
  text-decoration: none;
}
a {
  color: inherit;
  text-decoration: none;
}
a.txt:hover {
  text-decoration: underline;
}
img {
  max-width: 100%;
  max-height: 100%;
}
/* 透明按钮 */
.radio-transparent {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
  opacity: 0;
  filter: opacity(0%);
}
/**/
.clearfix:before,
.clearfix:after {
  content: '';
  display: table;
}
.clearfix:after {
  clear: both;
}
.scrollbar-primary::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.scrollbar-primary::-webkit-scrollbar-track {
  background-color: #c2bfbf;
  border-radius: 2px;
  box-shadow: inset 0 0 6px #ccc;
}
.scrollbar-primary::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #eee;
  -webkit-box-shadow: inset 0 0 6px #dfdfe3;
  box-shadow: inset 0 0 6px #dfdfe3;
}
.scrollbar-primary::-webkit-scrollbar-corner {
  background-color: transparent;
}
.placeholder-inherit::-webkit-input-placeholder {
  color: inherit;
}
.placeholder-inherit:-moz-placeholder {
  color: inherit;
}
.placeholder-inherit::-moz-placeholder {
  color: inherit;
}
.placeholder-inherit:-ms-input-placeholder {
  color: inherit;
}
.input-number-noappearance {
  -moz-appearance: textfield;
}
.input-number-noappearance::-webkit-inner-spin-button,
.input-number-noappearance::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.input-number-noappearance input[type='number'] {
  -moz-appearance: textfield;
}
.input-number-noappearance input[type='number']::-webkit-inner-spin-button,
.input-number-noappearance input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.btn-default-hook,
.btn-gray-default,
.btn-gray-default.van-button,
.btn-theme-brand-default,
.btn-theme-brand-default.van-button {
  height: 88px;
  border-radius: 50px;
  font-size: 32px;
  color: #fff;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
  box-shadow: none;
  margin: 0;
}
.btn-default-hook:after {
  content: '';
  display: none;
  line-height: 0;
}
.btn-gray-default {
  background: #f6f6f6;
  color: #777;
}
.btn-theme-brand-default {
  background: linear-gradient(90deg, #3D83FF 20%, #6742FF 100%);
}
