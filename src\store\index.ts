/*
 * @Author: 范非苗
 * @Date: 2025-04-24 10:07:06
 * @LastEditTime: 2025-04-29 10:11:50
 */
import { configureStore } from '@reduxjs/toolkit'
import loginResultReducer from './loginResult'

/*
导出切片的reducer，使用的时候传入{type:"user-slice/setName",payload:"李四"}
user-slice 是./user 里面定义的name；setName是./user 里面定义的reducer 的具体的函数。
*/
const store = configureStore({
  reducer: {
    loginResult: loginResultReducer
  }
})
export default store
