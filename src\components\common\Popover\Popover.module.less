//popover
.popover-wrap {
}
.popover-body {
  background-color: rgba(#333, 90%);
  color: #f1f2f3;
  font-size: 24px;
  line-height: 1.6;
  position: absolute;
  z-index: 1012;
  top: 0;
  left: 0;
  transform: translate(0%, -100%);
  border-radius: 16px;
  padding: 20px;
  text-align: center;


  .title {
    font-size: 28px;
    color: #fff;
  }
  .popover-content {
    max-height: 500px;
    overflow: auto;
  }
}
.popover-arrow {
  @h: 16px;
  position: absolute;
  left: 0;
  top:0;
  width: 40px;
  transform: translateX(-50%);
  height: @h;
  background-color: rgba(#333, 90%);
  clip-path: polygon(0% 0%, 50% 100%, 100% 0%);
}
