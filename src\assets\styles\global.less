@import './var.less';

// 文字
.font(@fontSize,@color,@fontWeight:@fontWeight-1,@lineHeight:1.5,@fontFamily:'PingFang SC') {
    color: @color;
    font-weight: @fontWeight;
    font-size: @fontSize;
    font-family: @fontFamily;
    line-height: @lineHeight;
}
// 小标题
.mini-title {
    .font(@fontSize-4,@textColor-1);
}
// 标准文本
.primary-text {
    .font(@fontSize-3,@textColor-1);
}

// 盒子
.wrap-box(@padding) {
    box-sizing: border-box;
    padding: @padding;
}
// 弹性盒子 垂直对齐
.flex-align-items-center {
    display: flex;
    align-items: center;
}
// 弹性盒子 水平对齐
.flex-justify-content-center {
    display: flex;
    justify-content: center;
}

// 弹性盒子 水平垂直对齐
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

// 底部固定
.footer-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 90;
    width: 100%;
}

// 卡片
.base-card(@padding) {
    background-color: #fff;
    border-radius: 16px;

    .wrap-box(@padding);
}
// 单行文本省略
.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
// 多行文本省略
.multi-ellipsis(@lines) {
    /* stylelint-disable-next-line value-no-vendor-prefix */
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: @lines;

    /* autoprefixer: ignore next */
    -webkit-box-orient: vertical;
}
.background-image(@url) {
    background-image: url(@url);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}
