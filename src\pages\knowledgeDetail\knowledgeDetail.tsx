/*
 * @Author: 范非苗
 * @Date: 2025-04-18 16:49:18
 * @LastEditTime: 2025-04-28 14:19:08
 */
import { View, Image, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import styles from './knowledgeDetail.module.less'
function Index() {
  // 可以使用所有的 React Hooks

  function onSureDelete() {
    Taro.showModal({
      title: '提示',
      content: '确定删除吗？',
      success: function (res) {
        if (res.confirm) {
          Taro.showToast({
            title: '删除成功',
            icon: 'none',
            duration: 2000
          })
          Taro.navigateBack()
        }
      }
    })
  }

  return (
    <>
      <View className="p-base  flex flex-column  mheight-100vh ">
        <View className="position-relative text-center my-5">
          <View className={`${styles.errPic}   mx-auto `}>
            <Image
              className="width-100 height-100"
              mode="aspectFit"
              src={require('@/assets/images/<EMAIL>')}
            />
          </View>
          <View className={`text-third font-s`}>网络出错了，点击重新加载或返回</View>
        </View>
        <View className="pt-5">
          <Button className="btn-theme-brand-default">重新加载</Button>
        </View>
      </View>
    </>
  )
}

export default Index
