/*
 * @Author: 范非苗
 * @Date: 2025-04-15 16:08:29
 * @LastEditTime: 2025-04-23 13:32:07
 */
import Taro from '@tarojs/taro'
import { View, Image } from '@tarojs/components'
import styles from './ListItem.module.less'

function Index({
  index = 0,
  item = { name: '', size: 0, status: '' ,_status:"",errorInfo:"",uploadTime:""},
  renderRight,
  className = 'bg-white',
  iconbgClassName = 'bg-white',
  onClick = () => {}
}) {
  //文件类型对应的icon
  function formatFileIcon(name: string = '') {
    if (!name) return ''
    const extension = name.split('.').pop()?.toLowerCase() || ''
    switch (extension) {
      case 'jpeg':
      case 'jpg':
        return require('@/assets/images/<EMAIL>')
      case 'png':
        return require('@/assets/images/<EMAIL>')
      case 'heic':
        return require('@/assets/images/<EMAIL>')
      case 'pdf':
        return require('@/assets/images/<EMAIL>')
      case 'doc':
      case 'docx':
        return require('@/assets/images/<EMAIL>')
      case 'xls':
      case 'xlsx':
        return require('@/assets/images/<EMAIL>')
      case 'ppt':
      case 'pptx':
        return require('@/assets/images/<EMAIL>')
      case 'txt':
        return require('@/assets/images/<EMAIL>')
      default:
        return require('@/assets/images/<EMAIL>')
    }
  }
  // 文件状态,目前是前端自己定义了几个
  /*
  WAIT:等待上传 UPLOADING:上传中  SIZE_EXCEEDS:大小超出限制 NOT_ALLOWED_FORMAT: 文件格式不支持  NAME_REPEAT :文件名重复
  */
  function formatStatusTitle(status: string = '') {
    const obj={
      className: 'text-primary',
      title: '上传失败'
    }
    switch (status.toLocaleUpperCase()) {
      case 'WAIT':
        obj.title= '等待上传'
        obj.className= 'text-primary'
        break
      case 'UPLOADING':
        obj.title= '上传中'
        obj.className= 'text-primary'
        break
      case 'SIZE_EXCEEDS':
        obj.title= '大小超出限制'
        obj.className= 'text-warning'
        break
      case 'NOT_ALLOWED_FORMAT':
        obj.title= '文件格式不支持'
        obj.className= 'text-warning'
        break
      case 'NAME_REPEAT':
        obj.title= '文件名重复'
        obj.className= 'text-warning'
        break

    }
    return obj
  }
  //  格式化时间
  function _formatTime(time=''){
    let str=time.split(" ")[0]
    let str1=str.split("-")
    const nowYear=new Date().getFullYear()
    str=str1[0]===nowYear.toString() ? `${str1[1]}-${str1[2]}`: str
    return str
  }
  // 格式化文件大小
  function _formatSize(size: number = 0) {
    return (size < 1024
      ? `${size}B`
      : size < 1024 * 1024
      ? `${(size / 1024).toFixed(0)}KB`
      : size < 1024 * 1024 * 1024
      ? `${(size / (1024 * 1024)).toFixed(1)}MB`
      : `${(size / (1024 * 1024 * 1024)).toFixed()}`)
  }

  return (
    <>
      <View className={`flex align-center  p-base rounded   ${className}`} onClick={onClick}>
        <View
          className={`mr-base flex-shrink flex align-center justify-center ${styles['item-thumb-box']} rounded ${iconbgClassName}`}
        >
          <Image
            className="width-100 height-100"
            mode="aspectFit"
            src={formatFileIcon(item.name)}
          />
        </View>
        <View className="flex-1">
          <View className="mb-1 text-first font-s lineh-1-2">{item.name}</View>
          <View className='flex align-center font-xs text-fourth'>
            <View className="mr-2">
              {_formatSize(item.size)}
            </View>
            <View>{_formatTime(item.uploadTime)}</View>
          </View>
          <View className=" font-xs">
            {item.errorInfo && <View className="text-warning">{item.errorInfo}</View>}
            {item._status && (
              <View className={formatStatusTitle(item._status).className}>{formatStatusTitle(item._status).title}</View>
            )}
          </View>
        </View>
        <View className="flex-shrink">{renderRight}</View>
      </View>
    </>
  )
}
export default Index
