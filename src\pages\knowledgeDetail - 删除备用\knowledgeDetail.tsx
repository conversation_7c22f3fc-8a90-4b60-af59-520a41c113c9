/*
 * @Author: 范非苗
 * @Date: 2025-04-18 16:49:18
 * @LastEditTime: 2025-04-21 14:04:02
 */
import React, { useEffect } from 'react'
import { View, Image, Button } from '@tarojs/components'
import Taro, { useReady, useDidShow, useDidHide, usePullDownRefresh } from '@tarojs/taro'
import styles from './knowledgeDetail.module.less'
function Index() {
  // 可以使用所有的 React Hooks
  useEffect(() => {
    console.log('useEffect')
  }, [])

  // 对应 onReady
  useReady(() => {
    console.log('useReady')
  })

  // 对应 onShow
  useDidShow(() => {
    console.log('useDidShow')
  })

  // 对应 onHide
  useDidHide(() => {
    console.log('useDidHide')
  })

  // Taro 对所有小程序页面生命周期都实现了对应的自定义 React Hooks 进行支持
  usePullDownRefresh(() => {
    console.log('usePullDownRefresh')
  })
  function onSureDelete() {
    Taro.showModal({
      title: '提示',
      content: '确定删除吗？',
      success: function (res) {
        if (res.confirm) {
          Taro.showToast({
            title: '删除成功',
            icon: 'none',
            duration: 2000
          })
          Taro.navigateBack()
        }
      }
    })
  }

  return (
    <>
      <View className="p-base  flex flex-column justify-center">
        <View className="text-center mb-9">
          <View className={`mb-base  ${styles['item-thumb-box']}  rounded bg-light-muted mx-auto`}>
            <Image
              className="width-100 height-100"
              mode="aspectFit"
              src={require('@/assets/images/<EMAIL>')}
            />
          </View>
          <View className="font-weight-bold font mb-1">内科心得</View>
          <View className="text-fourth font-xs">15kB 2024.10.12</View>
        </View>
        <View className="text-third font-s mb-base text-center">如需修改，请删除后重新上传</View>
        <View>
          <Button className="btn-theme-brand-default" onClick={onSureDelete}>
            删除文件
          </Button>
        </View>
      </View>
    </>
  )
}

export default Index
