/*
 * @Author: 范非苗
 * @Date: 2025-05-03 08:44:40
 * @LastEditTime: 2025-05-03 10:01:47
 */
import Taro from '@tarojs/taro'
import { BASE_URL } from './config.ts'
import store from '@/store'
import { clearLoginResult } from '@/store/loginResult'
import wx from 'weixin-js-sdk'


const TIMEOUT = 10000
const loginResult = store.getState()?.loginResult?.loginResult ?? {}
const redirectLoginUrl = decodeURIComponent(loginResult.redirectLoginUrl)
const wxname = loginResult.wxname
const common = (config = {}) => {

  const token = Taro.getStorageSync('token')
  let header = {
    'Authorization': `Bearer ${token}`,
  }  // 注意 Content-type  大小写
/*
Taro.request() 对于content-type 的封装是有点问题的，默认值为application/json。如果要用formdata 格式传递数据，就不要传content-type，让浏览器自己识别处理。multipart/form-data 的配置还少了一个boundary的属性配置，但是boundary的值是随机的自定义的需要和后端配合的
*/

  if (config.isUrlencoded) {
    header["Content-type"]="application/x-www-form-urlencoded;charset=utf-8"  //
  }

  switch (wxname) {
    case 'yuanyi':
      header = {
        ...header,
        biz:loginResult.biz
      }
      break
    case 'yubei':
      break
  }
  return {
    timeout: TIMEOUT,
    header: header,
    token: token
  }
}

// 文件上传
const uploadFile = (url = '', filePath = '', formData = {}, config = { native: false }) => {
  const { timeout, header } = common(config)
  Taro.uploadFile({
    url: BASE_URL + url,
    name: 'file',
    filePath: filePath,
    timeout: timeout,
    header: {
      ...header
    },
    formData: formData,
    success(res) {
      console.log('res', res)
      // 需要源数据返回
      if (config.native) {
        return Promise.resolve(res)
      }
      return Promise.resolve(JSON.parse(res.data))
    },
    fail(e) {
      console.error('uploadFile fail', e)
      return Promise.reject(e)
    },
    complete() {
    }
  })
}

const get = (url = '', data = {}, config = {}) => {
  const reqobj = {
    method: 'GET',
    url,
    data,
    ...config
  }
  return _request(reqobj)
}


const post = (url = '', data = {}, config = {}) => {
  const reqobj = {
    method: 'POST',
    url,
    data,
    ...config
  }
  return _request(reqobj)
}
const request = (url = '', data = {}, config = {}) => {
  const reqobj = {
    method: config.method ?? 'GET',
    url,
    data,
    ...config
  }
  return _request(reqobj)
}
// 封装Taro.request()
const _request = function (config) {
  const { timeout, header, token } = common(config)
  config.url=BASE_URL + config.url
  config = {
    ...config,
    timeout,
    header,
  }

  if (!config.noAuth) {
    // 默认需要权限，  token 过期则需要跳转到登录
    if (!token) {
      Taro.showToast({
        title: '无效token,请登录',
        icon: 'none'
      })
      return wx.miniProgram.reLaunch({
        url: redirectLoginUrl
      })
    }
  }
  return new Promise((resolve, reject) => {
    Taro.request({
      ...config,
      success(res) {

        // Taro.showModal({
        //   title: '提示2',
        //   content: JSON.stringify(res),
        //   showCancel: false
        // })
        // Taro.showModal({
        //   title: '提示',
        //   content: JSON.stringify(res),
        //   showCancel: false
        // })
        if (res.statusCode !== 200) {

          //  Taro.request请求出错
          console.log('request-success-error:', res)
          Taro.showToast({
            icon: 'none',
            title: `请求出错`,
            duration: 2000
          })
          return resolve(res)
        }
        // 需要源数据返回
        if (config.native) {
          return resolve(res)
        }
        // token 过期
        if (res.data.code ==401) {
          store.dispatch(clearLoginResult()) // 清除缓存
          Taro.showToast({
            icon: 'none',
            title: `token过期，正在为你跳转到登录页面`,
            duration: 3000
          })
          return wx.miniProgram.reLaunch({
            url: redirectLoginUrl
          })
        }
        // 返回数据
        return resolve(res.data)
      },
      fail(e) {
        console.log('request-fail', e)
        reject(e)
      },
      complete: () => {}
    })
  })
}

export default {
  get,
  post,
  uploadFile,
  request
}
